#!/usr/bin/env python3
"""
FinMAS Enhanced - Main Application Entry Point

Run this script to start the enhanced financial analysis system with Streamlit UI.

Usage:
    python app.py
    
Or with Streamlit directly:
    streamlit run app.py
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent))

# Import the main orchestrator
from main import main

if __name__ == "__main__":
    main()
