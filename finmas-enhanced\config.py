"""
Configuration settings for FinMAS Enhanced
"""

import os
from typing import Dict, Any
from pathlib import Path

# Base configuration
BASE_DIR = Path(__file__).parent
LOGS_DIR = BASE_DIR / "logs"
DATA_DIR = BASE_DIR / "data"

# Ensure directories exist
LOGS_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)

# AWS Bedrock Configuration
BEDROCK_CONFIG = {
    "default_model": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
    "region_name": "us-east-1",
    "max_retries": 3,
    "retry_delay": 1.0,
    "default_temperature": 0.1,
    "default_max_tokens": 2000
}

# Model pricing (per 1000 tokens)
MODEL_PRICING = {
    "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
        "input": 0.003,   # $3 per 1M input tokens
        "output": 0.015   # $15 per 1M output tokens
    },
    "anthropic.claude-3-sonnet-20240229-v1:0": {
        "input": 0.003,
        "output": 0.015
    },
    "anthropic.claude-3-haiku-20240307-v1:0": {
        "input": 0.00025,
        "output": 0.00125
    }
}

# Analysis Configuration
ANALYSIS_CONFIG = {
    "default_components": {
        "technical": True,
        "fundamental": True,
        "news": True,
        "recommendation": True
    },
    "similarity_top_k": 5,
    "max_news_articles": 20,
    "news_lookback_days": 30
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_rotation": "1 day",
    "max_log_files": 30
}

# Cost Tracking Configuration
COST_CONFIG = {
    "default_budget_limit": 10.0,  # $10 default session budget
    "alert_thresholds": {
        "budget_warning": 0.8,  # 80% of budget
        "high_cost_per_call": 0.1,  # $0.10 per call
        "high_token_usage": 3000  # tokens per call
    }
}

# Streamlit Configuration
STREAMLIT_CONFIG = {
    "page_title": "FinMAS Enhanced - Financial Analysis System",
    "page_icon": "📈",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# Environment Variables
def get_env_config() -> Dict[str, Any]:
    """Get configuration from environment variables"""
    return {
        "aws_access_key_id": os.getenv("AWS_ACCESS_KEY_ID"),
        "aws_secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY"),
        "aws_session_token": os.getenv("AWS_SESSION_TOKEN"),
        "aws_region": os.getenv("AWS_DEFAULT_REGION", BEDROCK_CONFIG["region_name"]),
        "log_level": os.getenv("LOG_LEVEL", LOGGING_CONFIG["level"]),
        "budget_limit": float(os.getenv("BUDGET_LIMIT", COST_CONFIG["default_budget_limit"]))
    }

# Available Models
AVAILABLE_MODELS = [
    {
        "id": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        "name": "Claude 3.5 Sonnet (Latest)",
        "description": "Most capable model, best for complex analysis",
        "cost_tier": "premium"
    },
    {
        "id": "anthropic.claude-3-sonnet-20240229-v1:0", 
        "name": "Claude 3 Sonnet",
        "description": "Balanced performance and cost",
        "cost_tier": "standard"
    },
    {
        "id": "anthropic.claude-3-haiku-20240307-v1:0",
        "name": "Claude 3 Haiku",
        "description": "Fastest and most cost-effective",
        "cost_tier": "economy"
    }
]

# Default Analysis Parameters
DEFAULT_ANALYSIS_PARAMS = {
    "temperature": 0.1,
    "max_tokens": 2000,
    "similarity_top_k": 5,
    "enable_memory": True,
    "enable_planning": True,
    "verbose": True
}

# Export all configurations
__all__ = [
    "BASE_DIR",
    "LOGS_DIR", 
    "DATA_DIR",
    "BEDROCK_CONFIG",
    "MODEL_PRICING",
    "ANALYSIS_CONFIG",
    "LOGGING_CONFIG",
    "COST_CONFIG",
    "STREAMLIT_CONFIG",
    "AVAILABLE_MODELS",
    "DEFAULT_ANALYSIS_PARAMS",
    "get_env_config"
]
