"""
FinMAS Chat Interface with AWS Bedrock Converse API

A conversational chat interface for financial analysis using AWS Bedrock's Converse API
with function calling and CrewAI agent orchestration.
"""

import streamlit as st
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import our modules
from bedrock_converse import BedrockConverseClient
from chat_agents import ChatFinancialAgents
from chat_function_schemas import get_all_function_names, validate_function_call


# Page configuration
st.set_page_config(
    page_title="FinMAS Chat - Financial Analysis Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)


def initialize_session_state():
    """Initialize session state variables."""
    if 'chat_client' not in st.session_state:
        st.session_state.chat_client = None
    if 'agents' not in st.session_state:
        st.session_state.agents = None
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    if 'aws_configured' not in st.session_state:
        st.session_state.aws_configured = False


def check_aws_credentials():
    """Check if AWS credentials are configured."""
    # Check environment variables first
    env_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY']
    env_configured = all(os.getenv(var) for var in env_vars)
    
    # Check session state for manually entered credentials
    session_configured = (
        hasattr(st.session_state, 'aws_access_key_id') and 
        hasattr(st.session_state, 'aws_secret_access_key') and
        st.session_state.aws_access_key_id and 
        st.session_state.aws_secret_access_key
    )
    
    # Check if boto3 can find credentials using default methods
    boto3_configured = False
    try:
        import boto3
        session = boto3.Session()
        credentials = session.get_credentials()
        boto3_configured = credentials is not None
    except Exception:
        boto3_configured = False
    
    return env_configured or session_configured or boto3_configured


def create_sidebar():
    """Create the sidebar with configuration options."""
    st.sidebar.title("🤖 FinMAS Chat")
    st.sidebar.markdown("*Financial Analysis Assistant*")
    
    # AWS Configuration
    st.sidebar.subheader("AWS Bedrock Configuration")
    
    aws_configured = check_aws_credentials()
    
    if not aws_configured:
        st.sidebar.warning("⚠️ AWS credentials not detected")
        
        with st.sidebar.expander("🔐 Enter AWS Credentials", expanded=True):
            aws_access_key = st.text_input(
                "AWS Access Key ID",
                type="password",
                help="Your AWS Access Key ID"
            )
            aws_secret_key = st.text_input(
                "AWS Secret Access Key", 
                type="password",
                help="Your AWS Secret Access Key"
            )
            aws_region = st.selectbox(
                "AWS Region",
                options=["us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"],
                index=0,
                help="AWS region where Bedrock is available"
            )
            
            if st.button("💾 Save Credentials"):
                if aws_access_key and aws_secret_key:
                    st.session_state.aws_access_key_id = aws_access_key
                    st.session_state.aws_secret_access_key = aws_secret_key
                    st.session_state.aws_region = aws_region
                    st.sidebar.success("✅ Credentials saved!")
                    st.rerun()
                else:
                    st.sidebar.error("❌ Please enter both Access Key ID and Secret Access Key")
        
        aws_configured = check_aws_credentials()
    
    if aws_configured:
        st.sidebar.success("✅ AWS credentials configured")
        
        # Model selection
        st.sidebar.subheader("Model Configuration")

        available_models = {
            "us.anthropic.claude-3-5-sonnet-20241022-v2:0": "Claude 3.5 Sonnet v2 (Latest)",
            "us.anthropic.claude-3-5-sonnet-20240620-v1:0": "Claude 3.5 Sonnet (Inference Profile)",
            "us.anthropic.claude-3-5-haiku-20241022-v1:0": "Claude 3.5 Haiku (Latest)",
            "anthropic.claude-3-5-sonnet-20240620-v1:0": "Claude 3.5 Sonnet (Direct)",
            "anthropic.claude-3-sonnet-20240229-v1:0": "Claude 3 Sonnet",
            "anthropic.claude-3-haiku-20240307-v1:0": "Claude 3 Haiku",
            "us.anthropic.claude-3-sonnet-20240229-v1:0": "Claude 3 Sonnet (Inference Profile)",
            "us.anthropic.claude-3-haiku-20240307-v1:0": "Claude 3 Haiku (Inference Profile)",
        }
        
        selected_model = st.sidebar.selectbox(
            "Bedrock Model",
            options=list(available_models.keys()),
            index=0,
            format_func=lambda x: available_models[x]
        )
        
        # Chat parameters
        st.sidebar.subheader("Chat Parameters")
        temperature = st.sidebar.slider("Temperature", 0.0, 1.0, 0.1, 0.1)
        max_tokens = st.sidebar.slider("Max Tokens", 500, 4000, 2000, 100)
        
        # Available functions
        st.sidebar.subheader("Available Functions")
        functions = get_all_function_names()
        st.sidebar.info(f"📋 {len(functions)} analysis functions available")
        
        with st.sidebar.expander("View Functions"):
            for func in functions:
                st.sidebar.text(f"• {func}")
        
        # Clear conversation
        if st.sidebar.button("🗑️ Clear Conversation"):
            st.session_state.messages = []
            if st.session_state.chat_client:
                st.session_state.chat_client.clear_conversation()
            st.rerun()
        
        # Export conversation
        if st.session_state.messages and st.sidebar.button("📥 Export Conversation"):
            if st.session_state.chat_client:
                export_data = st.session_state.chat_client.export_conversation()
                st.sidebar.download_button(
                    label="Download Chat History",
                    data=export_data,
                    file_name=f"finmas_chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
    
    return {
        'aws_configured': aws_configured,
        'selected_model': selected_model if aws_configured else None,
        'temperature': temperature if aws_configured else 0.1,
        'max_tokens': max_tokens if aws_configured else 2000
    }


def initialize_clients(config):
    """Initialize the chat client and agents."""
    if not config['aws_configured']:
        return False
    
    try:
        # Initialize Bedrock client
        client_kwargs = {
            'model_id': config['selected_model'],
            'region_name': getattr(st.session_state, 'aws_region', 'us-east-1')
        }
        
        # Add manual credentials if available
        if hasattr(st.session_state, 'aws_access_key_id'):
            client_kwargs.update({
                'aws_access_key_id': st.session_state.aws_access_key_id,
                'aws_secret_access_key': st.session_state.aws_secret_access_key
            })
        
        st.session_state.chat_client = BedrockConverseClient(**client_kwargs)
        
        # Initialize agents
        st.session_state.agents = ChatFinancialAgents(model_id=config['selected_model'])
        
        st.session_state.aws_configured = True
        return True
        
    except Exception as e:
        st.error(f"Failed to initialize clients: {str(e)}")
        return False


def display_chat_messages():
    """Display chat messages."""
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])


def process_user_message(user_input: str, config: Dict[str, Any]):
    """Process user message and generate response."""
    # Add user message to session
    st.session_state.messages.append({"role": "user", "content": user_input})
    
    with st.chat_message("user"):
        st.markdown(user_input)
    
    # Generate response
    with st.chat_message("assistant"):
        with st.spinner("Thinking..."):
            try:
                # Get response from Bedrock with function calling
                response_text, function_calls = st.session_state.chat_client.converse_with_functions(
                    user_input,
                    max_tokens=config['max_tokens'],
                    temperature=config['temperature']
                )
                
                # If there are function calls, execute them
                if function_calls:
                    st.info(f"🔧 Executing {len(function_calls)} function(s)...")
                    
                    function_results = []
                    for func_call in function_calls:
                        result = execute_function(func_call)
                        function_results.append(result)
                    
                    # Continue conversation with function results
                    final_response = st.session_state.chat_client.continue_conversation_with_function_results(
                        function_results,
                        max_tokens=config['max_tokens'],
                        temperature=config['temperature']
                    )
                    
                    st.markdown(final_response)
                    st.session_state.messages.append({"role": "assistant", "content": final_response})
                else:
                    # No function calls, just display the response
                    st.markdown(response_text)
                    st.session_state.messages.append({"role": "assistant", "content": response_text})
                
            except Exception as e:
                error_msg = f"I apologize, but I encountered an error: {str(e)}"
                st.error(error_msg)
                st.session_state.messages.append({"role": "assistant", "content": error_msg})


def execute_function(func_call: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a function call and return the result."""
    function_name = func_call['name']
    parameters = func_call['input']
    tool_use_id = func_call['toolUseId']

    try:
        # Map function names to analysis types
        if function_name == "analyze_stock":
            analysis_types = parameters.get('analysis_types', ['combined'])
            ticker = parameters.get('ticker', '').upper()
            timeframe = parameters.get('timeframe', '6mo')

            if not ticker:
                result = "Error: No ticker symbol provided."
            elif 'combined' in analysis_types:
                result = st.session_state.agents.run_analysis('combined', ticker, timeframe=timeframe)
            else:
                results = []
                for analysis_type in analysis_types:
                    analysis_result = st.session_state.agents.run_analysis(analysis_type, ticker, timeframe=timeframe)
                    results.append(f"## {analysis_type.title()} Analysis for {ticker}\n{analysis_result}")
                result = "\n\n".join(results)

        elif function_name == "get_technical_analysis":
            ticker = parameters.get('ticker', '').upper()
            timeframe = parameters.get('timeframe', '6mo')
            indicators = parameters.get('indicators', ['sma', 'rsi', 'volume'])

            if not ticker:
                result = "Error: No ticker symbol provided."
            else:
                result = st.session_state.agents.run_analysis('technical', ticker, timeframe=timeframe, indicators=indicators)

        elif function_name == "get_fundamental_analysis":
            ticker = parameters.get('ticker', '').upper()
            metrics = parameters.get('metrics', ['all'])
            compare_to_peers = parameters.get('compare_to_peers', False)

            if not ticker:
                result = "Error: No ticker symbol provided."
            else:
                result = st.session_state.agents.run_analysis('fundamental', ticker, metrics=metrics, compare_to_peers=compare_to_peers)

        elif function_name == "get_news_sentiment":
            ticker = parameters.get('ticker', '').upper()
            date_range = parameters.get('date_range', '1mo')
            max_articles = parameters.get('max_articles', 20)

            if not ticker:
                result = "Error: No ticker symbol provided."
            else:
                result = st.session_state.agents.run_analysis('news', ticker, date_range=date_range, max_articles=max_articles)

        elif function_name == "analyze_sec_filings":
            ticker = parameters.get('ticker', '').upper()
            filing_types = parameters.get('filing_types', ['10-K', '10-Q'])
            max_filings = parameters.get('max_filings', 5)
            focus_areas = parameters.get('focus_areas', ['financial_highlights', 'risk_factors'])

            if not ticker:
                result = "Error: No ticker symbol provided."
            else:
                # For now, use news analysis as a placeholder for SEC analysis
                result = f"SEC Filing Analysis for {ticker} (Filing types: {', '.join(filing_types)}):\n\n"
                result += st.session_state.agents.run_analysis('news', ticker)

        elif function_name == "compare_stocks":
            tickers = [t.upper() for t in parameters.get('tickers', [])]
            comparison_metrics = parameters.get('comparison_metrics', ['fundamental', 'performance'])
            timeframe = parameters.get('timeframe', '6mo')

            if len(tickers) < 2:
                result = "Error: At least 2 ticker symbols required for comparison."
            else:
                results = []
                for ticker in tickers:
                    for metric in comparison_metrics:
                        if metric in ['technical', 'fundamental', 'news_sentiment']:
                            analysis_type = metric.replace('_sentiment', '')
                            ticker_result = st.session_state.agents.run_analysis(analysis_type, ticker, timeframe=timeframe)
                            results.append(f"### {ticker} - {metric.title()}\n{ticker_result}")

                result = f"## Stock Comparison: {' vs '.join(tickers)}\n\n" + "\n\n".join(results)

        elif function_name == "get_market_overview":
            sectors = parameters.get('sectors', [])
            include_indices = parameters.get('include_indices', True)
            timeframe = parameters.get('timeframe', '1mo')

            # Use SPY as a proxy for market overview
            result = f"Market Overview (Timeframe: {timeframe}):\n\n"
            result += st.session_state.agents.run_analysis('technical', 'SPY', timeframe=timeframe)

            if sectors:
                result += f"\n\nSector Analysis for: {', '.join(sectors)}"

        elif function_name == "get_investment_recommendation":
            ticker = parameters.get('ticker', '').upper()
            investment_horizon = parameters.get('investment_horizon', 'medium_term')
            risk_tolerance = parameters.get('risk_tolerance', 'moderate')
            portfolio_size = parameters.get('portfolio_size', 'medium')

            if not ticker:
                result = "Error: No ticker symbol provided."
            else:
                result = f"Investment Recommendation for {ticker}\n"
                result += f"Investment Horizon: {investment_horizon}, Risk Tolerance: {risk_tolerance}, Portfolio Size: {portfolio_size}\n\n"
                result += st.session_state.agents.run_analysis('combined', ticker)

        else:
            result = f"Function {function_name} is not yet implemented."

        return {
            'toolUseId': tool_use_id,
            'content': [{"text": result}],
            'status': 'success'
        }

    except Exception as e:
        error_msg = f"Error executing {function_name}: {str(e)}"
        return {
            'toolUseId': tool_use_id,
            'content': [{"text": error_msg}],
            'status': 'error'
        }


def main():
    """Main application function."""
    initialize_session_state()
    
    # Title
    st.title("🤖 FinMAS Chat")
    st.markdown("**Financial Analysis Assistant powered by AWS Bedrock & CrewAI**")
    
    # Create sidebar and get configuration
    config = create_sidebar()
    
    if not config['aws_configured']:
        st.warning("⚠️ Please configure your AWS credentials in the sidebar to start chatting.")
        st.info("""
        **Welcome to FinMAS Chat!** 
        
        I'm your AI-powered financial analysis assistant. Once you configure your AWS credentials, you can ask me to:
        
        - 📈 **Analyze stocks**: "Analyze AAPL's technical indicators"
        - 📊 **Compare companies**: "Compare AAPL and MSFT fundamentals"
        - 📰 **Check news sentiment**: "What's the recent news sentiment for TSLA?"
        - 💰 **Get recommendations**: "Should I invest in NVDA for long-term?"
        - 🔍 **Market overview**: "How is the tech sector performing?"
        
        Just type your questions in natural language and I'll use my specialized AI agents to provide comprehensive analysis!
        """)
        return
    
    # Initialize clients if not already done
    if not st.session_state.chat_client or not st.session_state.agents:
        if not initialize_clients(config):
            return
    
    # Display chat messages
    display_chat_messages()
    
    # Chat input
    if user_input := st.chat_input("Ask me about stocks, market analysis, or investment advice..."):
        process_user_message(user_input, config)


if __name__ == "__main__":
    main()
