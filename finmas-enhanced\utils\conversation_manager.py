"""
Conversational Flow Manager for FinMAS Enhanced

This module manages chat history, context, and conversational flow
for the natural language financial analysis interface.
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

from .logging_utils import get_logger
from .function_schemas import (
    extract_intent_from_message, 
    extract_ticker_from_message,
    validate_function_call,
    get_function_description
)


@dataclass
class ChatMessage:
    """Represents a chat message"""
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None
    function_calls: Optional[List[Dict[str, Any]]] = None
    cost_info: Optional[Dict[str, Any]] = None


@dataclass
class ConversationContext:
    """Maintains conversation context and state"""
    current_ticker: Optional[str] = None
    last_analysis_type: Optional[str] = None
    user_preferences: Optional[Dict[str, Any]] = None
    session_start_time: Optional[str] = None
    total_interactions: int = 0


class ConversationManager:
    """Manages conversational flow and context for financial analysis"""
    
    def __init__(self):
        self.logger = get_logger()
        self.chat_history: List[ChatMessage] = []
        self.context = ConversationContext(
            session_start_time=datetime.now().isoformat(),
            user_preferences={}
        )
        
        # Initialize with welcome message
        self._add_welcome_message()
        
        self.logger.logger.info("Conversation Manager initialized")
    
    def _add_welcome_message(self):
        """Add initial welcome message to chat history"""
        welcome_msg = """👋 **Welcome to FinMAS Enhanced!**

I'm your AI financial analyst assistant. I can help you with:

🔍 **Stock Analysis** - "Analyze AAPL" or "What's the outlook for Microsoft?"
📊 **Technical Analysis** - "Show me technical indicators for TSLA"
📈 **Fundamental Analysis** - "What are the fundamentals of GOOGL?"
📰 **News & Sentiment** - "What's the recent news on NVDA?"
💡 **Investment Recommendations** - "Should I invest in Amazon?"
⚖️ **Stock Comparisons** - "Compare AAPL vs MSFT"
💰 **Cost Tracking** - "What are my session costs?"

Just ask me anything about stocks in natural language! What would you like to analyze?"""
        
        welcome_message = ChatMessage(
            role="assistant",
            content=welcome_msg,
            timestamp=datetime.now().isoformat(),
            metadata={"type": "welcome", "system_message": True}
        )
        
        self.chat_history.append(welcome_message)
    
    def add_user_message(self, content: str) -> ChatMessage:
        """Add a user message to chat history"""
        message = ChatMessage(
            role="user",
            content=content,
            timestamp=datetime.now().isoformat(),
            metadata={"user_input": True}
        )
        
        self.chat_history.append(message)
        self.context.total_interactions += 1
        
        # Update context based on message
        self._update_context_from_message(content)
        
        self.logger.logger.info(f"User message added: {content[:100]}...")
        return message
    
    def add_assistant_message(
        self, 
        content: str, 
        function_calls: Optional[List[Dict[str, Any]]] = None,
        cost_info: Optional[Dict[str, Any]] = None
    ) -> ChatMessage:
        """Add an assistant message to chat history"""
        message = ChatMessage(
            role="assistant",
            content=content,
            timestamp=datetime.now().isoformat(),
            function_calls=function_calls,
            cost_info=cost_info,
            metadata={"assistant_response": True}
        )
        
        self.chat_history.append(message)
        
        self.logger.logger.info(f"Assistant message added with {len(function_calls or [])} function calls")
        return message
    
    def _update_context_from_message(self, message: str):
        """Update conversation context based on user message"""
        # Extract tickers
        tickers = extract_ticker_from_message(message)
        if tickers:
            self.context.current_ticker = tickers[0]  # Use first ticker found
        
        # Extract intents to update last analysis type
        intents = extract_intent_from_message(message)
        if intents:
            self.context.last_analysis_type = intents[0]
    
    def get_conversation_for_bedrock(self) -> List[Dict[str, Any]]:
        """Get conversation history formatted for Bedrock Converse API"""
        bedrock_messages = []
        
        for message in self.chat_history:
            # Skip system welcome message for Bedrock
            if message.metadata and message.metadata.get("system_message"):
                continue
            
            bedrock_message = {
                "role": message.role,
                "content": [{"text": message.content}]
            }
            
            # Add function calls if present
            if message.function_calls:
                for func_call in message.function_calls:
                    bedrock_message["content"].append({
                        "toolUse": {
                            "toolUseId": func_call.get("toolUseId", f"call_{int(time.time())}"),
                            "name": func_call["name"],
                            "input": func_call["input"]
                        }
                    })
            
            bedrock_messages.append(bedrock_message)
        
        return bedrock_messages
    
    def analyze_user_intent(self, message: str) -> Dict[str, Any]:
        """Analyze user intent and suggest function calls"""
        intents = extract_intent_from_message(message)
        tickers = extract_ticker_from_message(message)
        
        # If no ticker in current message, use context
        if not tickers and self.context.current_ticker:
            tickers = [self.context.current_ticker]
        
        analysis_suggestion = {
            "intents": intents,
            "tickers": tickers,
            "suggested_functions": [],
            "context": {
                "current_ticker": self.context.current_ticker,
                "last_analysis": self.context.last_analysis_type
            }
        }
        
        # Suggest functions based on intents
        if not intents and tickers:
            # Default to comprehensive analysis if ticker mentioned but no specific intent
            analysis_suggestion["suggested_functions"].append({
                "name": "analyze_stock",
                "parameters": {
                    "ticker": tickers[0],
                    "analysis_types": ["all"]
                }
            })
        
        elif "technical_analysis" in intents and tickers:
            analysis_suggestion["suggested_functions"].append({
                "name": "get_technical_analysis",
                "parameters": {
                    "ticker": tickers[0],
                    "indicators": ["all"]
                }
            })
        
        elif "fundamental_analysis" in intents and tickers:
            analysis_suggestion["suggested_functions"].append({
                "name": "get_fundamental_analysis",
                "parameters": {
                    "ticker": tickers[0],
                    "focus_areas": ["all"]
                }
            })
        
        elif "news_sentiment" in intents and tickers:
            analysis_suggestion["suggested_functions"].append({
                "name": "get_news_sentiment",
                "parameters": {
                    "ticker": tickers[0],
                    "days_back": 30
                }
            })
        
        elif "investment_recommendation" in intents and tickers:
            analysis_suggestion["suggested_functions"].append({
                "name": "get_investment_recommendation",
                "parameters": {
                    "ticker": tickers[0],
                    "risk_tolerance": "moderate"
                }
            })
        
        elif "comparison" in intents and len(tickers) >= 2:
            analysis_suggestion["suggested_functions"].append({
                "name": "compare_stocks",
                "parameters": {
                    "tickers": tickers[:5],  # Limit to 5 stocks
                    "comparison_metrics": ["all"]
                }
            })
        
        elif "cost_tracking" in intents:
            analysis_suggestion["suggested_functions"].append({
                "name": "get_session_cost_summary",
                "parameters": {}
            })
        
        return analysis_suggestion
    
    def get_context_summary(self) -> str:
        """Get a summary of current conversation context"""
        summary_parts = []
        
        if self.context.current_ticker:
            summary_parts.append(f"Current focus: {self.context.current_ticker}")
        
        if self.context.last_analysis_type:
            summary_parts.append(f"Last analysis: {self.context.last_analysis_type}")
        
        summary_parts.append(f"Interactions: {self.context.total_interactions}")
        
        # Get cost summary
        cost_summary = self.logger.cost_tracker.get_session_summary()
        summary_parts.append(f"Session cost: ${cost_summary['total_cost']:.4f}")
        
        return " | ".join(summary_parts)
    
    def clear_conversation(self):
        """Clear conversation history and reset context"""
        self.chat_history = []
        self.context = ConversationContext(
            session_start_time=datetime.now().isoformat(),
            user_preferences={}
        )
        
        # Re-add welcome message
        self._add_welcome_message()
        
        self.logger.logger.info("Conversation cleared and reset")
    
    def export_conversation(self) -> Dict[str, Any]:
        """Export conversation history and context"""
        return {
            "session_info": {
                "start_time": self.context.session_start_time,
                "total_interactions": self.context.total_interactions,
                "export_time": datetime.now().isoformat()
            },
            "context": asdict(self.context),
            "chat_history": [asdict(msg) for msg in self.chat_history],
            "cost_summary": self.logger.cost_tracker.get_session_summary()
        }
    
    def get_recent_messages(self, count: int = 10) -> List[ChatMessage]:
        """Get recent messages for context"""
        return self.chat_history[-count:] if len(self.chat_history) > count else self.chat_history
    
    def format_message_for_display(self, message: ChatMessage) -> Dict[str, Any]:
        """Format message for Streamlit chat display"""
        display_message = {
            "role": message.role,
            "content": message.content,
            "timestamp": message.timestamp
        }
        
        # Add cost info if available
        if message.cost_info:
            cost_text = f"\n\n💰 **Cost:** ${message.cost_info.get('total_cost', 0):.4f} | "
            cost_text += f"Tokens: {message.cost_info.get('total_tokens', 0):,} | "
            cost_text += f"Duration: {message.cost_info.get('duration_ms', 0):.0f}ms"
            display_message["content"] += cost_text
        
        # Add function call info if available
        if message.function_calls:
            func_text = f"\n\n🔧 **Functions called:** {', '.join([fc['name'] for fc in message.function_calls])}"
            display_message["content"] += func_text
        
        return display_message
