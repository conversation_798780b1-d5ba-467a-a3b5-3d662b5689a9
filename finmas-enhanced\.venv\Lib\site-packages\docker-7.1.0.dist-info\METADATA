Metadata-Version: 2.3
Name: docker
Version: 7.1.0
Summary: A Python library for the Docker Engine API.
Project-URL: Changelog, https://docker-py.readthedocs.io/en/stable/change-log.html
Project-URL: Documentation, https://docker-py.readthedocs.io
Project-URL: Homepage, https://github.com/docker/docker-py
Project-URL: Source, https://github.com/docker/docker-py
Project-URL: Tracker, https://github.com/docker/docker-py/issues
Maintainer-email: "Docker Inc." <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development
Classifier: Topic :: Utilities
Requires-Python: >=3.8
Requires-Dist: pywin32>=304; sys_platform == 'win32'
Requires-Dist: requests>=2.26.0
Requires-Dist: urllib3>=1.26.0
Provides-Extra: dev
Requires-Dist: coverage==7.2.7; extra == 'dev'
Requires-Dist: pytest-cov==4.1.0; extra == 'dev'
Requires-Dist: pytest-timeout==2.1.0; extra == 'dev'
Requires-Dist: pytest==7.4.2; extra == 'dev'
Requires-Dist: ruff==0.1.8; extra == 'dev'
Provides-Extra: docs
Requires-Dist: myst-parser==0.18.0; extra == 'docs'
Requires-Dist: sphinx==5.1.1; extra == 'docs'
Provides-Extra: ssh
Requires-Dist: paramiko>=2.4.3; extra == 'ssh'
Provides-Extra: tls
Provides-Extra: websockets
Requires-Dist: websocket-client>=1.3.0; extra == 'websockets'
Description-Content-Type: text/markdown

# Docker SDK for Python

[![Build Status](https://github.com/docker/docker-py/actions/workflows/ci.yml/badge.svg)](https://github.com/docker/docker-py/actions/workflows/ci.yml)

A Python library for the Docker Engine API. It lets you do anything the `docker` command does, but from within Python apps – run containers, manage containers, manage Swarms, etc.

## Installation

The latest stable version [is available on PyPI](https://pypi.python.org/pypi/docker/). Install with pip:

    pip install docker

> Older versions (< 6.0) required installing `docker[tls]` for SSL/TLS support.
> This is no longer necessary and is a no-op, but is supported for backwards compatibility.

## Usage

Connect to Docker using the default socket or the configuration in your environment:

```python
import docker
client = docker.from_env()
```

You can run containers:

```python
>>> client.containers.run("ubuntu:latest", "echo hello world")
'hello world\n'
```

You can run containers in the background:

```python
>>> client.containers.run("bfirsh/reticulate-splines", detach=True)
<Container '45e6d2de7c54'>
```

You can manage containers:

```python
>>> client.containers.list()
[<Container '45e6d2de7c54'>, <Container 'db18e4f20eaa'>, ...]

>>> container = client.containers.get('45e6d2de7c54')

>>> container.attrs['Config']['Image']
"bfirsh/reticulate-splines"

>>> container.logs()
"Reticulating spline 1...\n"

>>> container.stop()
```

You can stream logs:

```python
>>> for line in container.logs(stream=True):
...   print(line.strip())
Reticulating spline 2...
Reticulating spline 3...
...
```

You can manage images:

```python
>>> client.images.pull('nginx')
<Image 'nginx'>

>>> client.images.list()
[<Image 'ubuntu'>, <Image 'nginx'>, ...]
```

[Read the full documentation](https://docker-py.readthedocs.io) to see everything you can do.
