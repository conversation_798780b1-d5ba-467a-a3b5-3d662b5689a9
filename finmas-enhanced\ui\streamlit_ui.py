"""
Enhanced Streamlit UI for FinMAS

This module provides a focused Streamlit interface for:
- User interaction and input
- Results presentation with clear organization
- Real-time cost tracking and analytics display
- Conversational chat interface
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import json

from ..utils.logging_utils import get_logger
from ..utils.crewai_utils import CrewExecutionResult, AgentResult


class FinMASStreamlitApp:
    """Enhanced Streamlit application for FinMAS financial analysis"""
    
    def __init__(self):
        self.logger = get_logger()
        self.setup_page_config()
        self.initialize_session_state()
    
    def setup_page_config(self):
        """Configure Streamlit page settings"""
        st.set_page_config(
            page_title="FinMAS Enhanced - Financial Analysis System",
            page_icon="📈",
            layout="wide",
            initial_sidebar_state="expanded"
        )
    
    def initialize_session_state(self):
        """Initialize Streamlit session state variables"""
        if 'analysis_results' not in st.session_state:
            st.session_state.analysis_results = []
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = []
        if 'current_ticker' not in st.session_state:
            st.session_state.current_ticker = ""
        if 'analysis_in_progress' not in st.session_state:
            st.session_state.analysis_in_progress = False
    
    def render_header(self):
        """Render the application header"""
        st.title("🚀 FinMAS Enhanced")
        st.subheader("Financial Multi-Agent System with Comprehensive Analytics")
        
        # Cost tracking display
        cost_summary = self.logger.cost_tracker.get_session_summary()
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Session Cost", f"${cost_summary['total_cost']:.4f}")
        with col2:
            st.metric("Total API Calls", cost_summary['total_calls'])
        with col3:
            st.metric("Total Tokens", f"{cost_summary['total_tokens']:,}")
        with col4:
            avg_cost = cost_summary['average_cost_per_call']
            st.metric("Avg Cost/Call", f"${avg_cost:.4f}")
    
    def render_sidebar(self):
        """Render the sidebar with controls and settings"""
        with st.sidebar:
            st.header("Analysis Settings")
            
            # Ticker input
            ticker = st.text_input(
                "Stock Ticker",
                value=st.session_state.current_ticker,
                placeholder="e.g., AAPL, MSFT, GOOGL"
            ).upper()
            
            # Model selection
            model_options = [
                "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                "anthropic.claude-3-sonnet-20240229-v1:0",
                "anthropic.claude-3-haiku-20240307-v1:0"
            ]
            selected_model = st.selectbox("Bedrock Model", model_options)
            
            # Analysis parameters
            st.subheader("Analysis Parameters")
            temperature = st.slider("Temperature", 0.0, 1.0, 0.1, 0.1)
            max_tokens = st.slider("Max Tokens", 500, 4000, 2000, 100)
            
            # Analysis type selection
            st.subheader("Analysis Components")
            run_technical = st.checkbox("Technical Analysis", True)
            run_fundamental = st.checkbox("Fundamental Analysis", True)
            run_news = st.checkbox("News Analysis", True)
            run_recommendation = st.checkbox("Investment Recommendation", True)
            
            # Action buttons
            st.subheader("Actions")
            
            if st.button("🔍 Run Complete Analysis", disabled=st.session_state.analysis_in_progress):
                if ticker:
                    st.session_state.current_ticker = ticker
                    return {
                        'action': 'run_analysis',
                        'ticker': ticker,
                        'model': selected_model,
                        'temperature': temperature,
                        'max_tokens': max_tokens,
                        'components': {
                            'technical': run_technical,
                            'fundamental': run_fundamental,
                            'news': run_news,
                            'recommendation': run_recommendation
                        }
                    }
                else:
                    st.error("Please enter a stock ticker")
            
            if st.button("💬 Start Chat Mode"):
                return {'action': 'start_chat'}
            
            if st.button("📊 View Cost Analytics"):
                return {'action': 'show_analytics'}
            
            if st.button("🗑️ Clear Session"):
                return {'action': 'clear_session'}
        
        return None
    
    def render_analysis_results(self, execution_result: CrewExecutionResult):
        """Render comprehensive analysis results"""
        st.header(f"📈 Analysis Results for {execution_result.ticker}")
        
        # Execution summary
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Execution Time", f"{execution_result.total_duration_ms:.0f}ms")
        with col2:
            st.metric("Total Cost", f"${execution_result.total_cost:.4f}")
        with col3:
            st.metric("Total Tokens", f"{execution_result.total_tokens:,}")
        
        # Agent results tabs
        agent_tabs = st.tabs([result.agent_name for result in execution_result.agent_results] + ["📋 Executive Summary"])
        
        # Individual agent results
        for i, result in enumerate(execution_result.agent_results):
            with agent_tabs[i]:
                self.render_agent_result(result)
        
        # Executive summary
        with agent_tabs[-1]:
            st.subheader("Executive Summary")
            st.markdown(execution_result.final_output)
            
            # Download options
            col1, col2 = st.columns(2)
            with col1:
                if st.button("📥 Download Full Report"):
                    self.download_report(execution_result)
            with col2:
                if st.button("📊 Export Data"):
                    self.export_analysis_data(execution_result)
    
    def render_agent_result(self, result: AgentResult):
        """Render individual agent result"""
        # Agent performance metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Execution Time", f"{result.execution_time_ms:.0f}ms")
        with col2:
            st.metric("Cost", f"${result.cost:.4f}")
        with col3:
            st.metric("Output Length", f"{len(result.output):,} chars")
        with col4:
            status_color = "🟢" if result.success else "🔴"
            st.metric("Status", f"{status_color} {'Success' if result.success else 'Failed'}")
        
        # Agent output
        if result.success:
            st.subheader(f"{result.task_name} Results")
            st.markdown(result.output)
        else:
            st.error(f"Agent execution failed: {result.error_message}")
        
        # Token usage breakdown
        if result.token_usage:
            with st.expander("Token Usage Details"):
                token_df = pd.DataFrame([result.token_usage])
                st.dataframe(token_df)
    
    def render_cost_analytics(self):
        """Render detailed cost analytics"""
        st.header("💰 Cost Analytics Dashboard")
        
        cost_summary = self.logger.cost_tracker.get_session_summary()
        api_calls = self.logger.cost_tracker.api_calls
        
        if not api_calls:
            st.info("No API calls recorded yet. Run an analysis to see cost data.")
            return
        
        # Cost overview
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Session Cost", f"${cost_summary['total_cost']:.4f}")
        with col2:
            st.metric("Total API Calls", cost_summary['total_calls'])
        with col3:
            st.metric("Total Tokens", f"{cost_summary['total_tokens']:,}")
        with col4:
            st.metric("Average Cost per Call", f"${cost_summary['average_cost_per_call']:.4f}")
        
        # Cost breakdown by model
        if cost_summary['model_breakdown']:
            st.subheader("Cost Breakdown by Model")
            model_df = pd.DataFrame([
                {"Model": model, "Cost": cost}
                for model, cost in cost_summary['model_breakdown'].items()
            ])
            
            fig = px.pie(model_df, values='Cost', names='Model', title="Cost Distribution by Model")
            st.plotly_chart(fig, use_container_width=True)
        
        # API calls timeline
        st.subheader("API Calls Timeline")
        if api_calls:
            calls_df = pd.DataFrame([
                {
                    "Timestamp": call.timestamp,
                    "Model": call.model_id.split(':')[0].split('.')[-1],  # Simplified model name
                    "Input Tokens": call.input_tokens,
                    "Output Tokens": call.output_tokens,
                    "Total Cost": call.total_cost,
                    "Duration (ms)": call.duration_ms
                }
                for call in api_calls
            ])
            
            # Convert timestamp to datetime
            calls_df['Timestamp'] = pd.to_datetime(calls_df['Timestamp'])
            
            # Cost over time
            fig = px.line(calls_df, x='Timestamp', y='Total Cost', title="Cost Over Time")
            st.plotly_chart(fig, use_container_width=True)
            
            # Token usage
            fig2 = go.Figure()
            fig2.add_trace(go.Scatter(x=calls_df['Timestamp'], y=calls_df['Input Tokens'], name='Input Tokens'))
            fig2.add_trace(go.Scatter(x=calls_df['Timestamp'], y=calls_df['Output Tokens'], name='Output Tokens'))
            fig2.update_layout(title="Token Usage Over Time", xaxis_title="Time", yaxis_title="Tokens")
            st.plotly_chart(fig2, use_container_width=True)
            
            # Detailed calls table
            st.subheader("Detailed API Calls")
            st.dataframe(calls_df, use_container_width=True)
    
    def render_chat_interface(self):
        """Render conversational chat interface"""
        st.header("💬 Chat with FinMAS")
        
        # Chat history
        for message in st.session_state.chat_history:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
        
        # Chat input
        if prompt := st.chat_input("Ask me about financial analysis..."):
            # Add user message
            st.session_state.chat_history.append({"role": "user", "content": prompt})
            with st.chat_message("user"):
                st.markdown(prompt)
            
            # Process and respond (placeholder for now)
            with st.chat_message("assistant"):
                response = "I'm ready to help with financial analysis! Please run a complete analysis first to get detailed insights."
                st.markdown(response)
                st.session_state.chat_history.append({"role": "assistant", "content": response})
    
    def download_report(self, execution_result: CrewExecutionResult):
        """Generate downloadable report"""
        report_content = f"""
# Financial Analysis Report for {execution_result.ticker}
Generated: {execution_result.end_time}

## Executive Summary
{execution_result.final_output}

## Performance Metrics
- Total Execution Time: {execution_result.total_duration_ms:.2f}ms
- Total Cost: ${execution_result.total_cost:.4f}
- Total Tokens: {execution_result.total_tokens:,}
- Success Rate: {sum(1 for r in execution_result.agent_results if r.success)}/{len(execution_result.agent_results)}
"""
        
        st.download_button(
            label="📥 Download Report",
            data=report_content,
            file_name=f"finmas_report_{execution_result.ticker}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            mime="text/markdown"
        )
    
    def export_analysis_data(self, execution_result: CrewExecutionResult):
        """Export analysis data as JSON"""
        export_data = {
            "ticker": execution_result.ticker,
            "timestamp": execution_result.end_time,
            "total_cost": execution_result.total_cost,
            "total_tokens": execution_result.total_tokens,
            "agent_results": [
                {
                    "agent_name": result.agent_name,
                    "task_name": result.task_name,
                    "success": result.success,
                    "execution_time_ms": result.execution_time_ms,
                    "cost": result.cost,
                    "output_length": len(result.output),
                    "token_usage": result.token_usage
                }
                for result in execution_result.agent_results
            ]
        }
        
        st.download_button(
            label="📊 Export Data",
            data=json.dumps(export_data, indent=2),
            file_name=f"finmas_data_{execution_result.ticker}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
    
    def clear_session(self):
        """Clear session data"""
        st.session_state.analysis_results = []
        st.session_state.chat_history = []
        st.session_state.current_ticker = ""
        st.session_state.analysis_in_progress = False
        
        # Clear logger data
        self.logger.cost_tracker.total_cost = 0.0
        self.logger.cost_tracker.api_calls = []
        self.logger.performance_monitor.function_calls = []
        self.logger.performance_monitor.agent_executions = []
        
        st.success("Session cleared successfully!")
        st.rerun()
    
    def run(self):
        """Main application entry point"""
        self.render_header()
        
        # Handle sidebar actions
        action = self.render_sidebar()
        
        if action:
            if action['action'] == 'clear_session':
                self.clear_session()
            elif action['action'] == 'show_analytics':
                self.render_cost_analytics()
            elif action['action'] == 'start_chat':
                self.render_chat_interface()
            elif action['action'] == 'run_analysis':
                # This will be handled by the main orchestrator
                return action
        
        # Display recent results
        if st.session_state.analysis_results:
            latest_result = st.session_state.analysis_results[-1]
            self.render_analysis_results(latest_result)
        else:
            # Welcome message
            st.info("👋 Welcome to FinMAS Enhanced! Enter a stock ticker and click 'Run Complete Analysis' to get started.")
        
        return None
