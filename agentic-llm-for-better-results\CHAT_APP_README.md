# FinMAS Chat - Conversational Financial Analysis Assistant

A sophisticated conversational chat interface for financial analysis powered by AWS Bedrock's Converse API with function calling and CrewAI agent orchestration.

## 🎯 Overview

FinMAS Chat transforms the traditional form-based financial analysis interface into a natural language conversation. Users can simply ask questions about stocks, markets, and investments, and the AI assistant will automatically:

- Parse the request using AWS Bedrock's Converse API
- Determine which analysis functions to call
- Orchestrate specialized AI agents for different analysis types
- Provide comprehensive, actionable insights

## ✨ Key Features

### 🤖 **Conversational Interface**
- Natural language input - no forms or complex configurations
- Persistent conversation history with context awareness
- Real-time function calling and analysis execution
- Export conversation history for future reference

### 🧠 **AI Agent Orchestration**
- **Technical Analyst Agent**: Price trends, indicators, trading signals
- **Fundamental Analyst Agent**: Financial health, valuation, growth prospects
- **News & Sentiment Agent**: Market sentiment, news impact analysis
- **Investment Advisor Agent**: Comprehensive recommendations synthesis

### 📊 **Comprehensive Analysis Functions**
- `analyze_stock`: Complete multi-faceted stock analysis
- `get_technical_analysis`: Technical indicators and chart patterns
- `get_fundamental_analysis`: Financial metrics and company health
- `get_news_sentiment`: News sentiment and market perception
- `analyze_sec_filings`: SEC filing insights and regulatory analysis
- `compare_stocks`: Multi-stock comparative analysis
- `get_market_overview`: Market conditions and sector trends
- `get_investment_recommendation`: Personalized investment advice

### 🔧 **Advanced Technology Stack**
- **AWS Bedrock Converse API**: Function calling and conversation management
- **CrewAI**: Multi-agent orchestration and task coordination
- **Streamlit**: Interactive web interface
- **Real-time Data**: Live stock data, news, and market information

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- AWS account with Bedrock access
- AWS credentials configured

### Installation

1. **Install dependencies**:
```bash
pip install streamlit boto3 yfinance pandas crewai crewai-tools
```

2. **Configure AWS credentials** (choose one method):

**Option A: Environment Variables**
```bash
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

**Option B: AWS CLI**
```bash
aws configure
```

**Option C: Manual Entry** (in the app sidebar)

3. **Run the application**:
```bash
streamlit run chat_app.py
```

4. **Open your browser** to `http://localhost:8501`

## 💬 Usage Examples

### Basic Stock Analysis
```
User: "Analyze AAPL's technical indicators for the last 6 months"
Assistant: [Executes technical analysis and provides detailed insights]
```

### Multi-Stock Comparison
```
User: "Compare AAPL and MSFT fundamentals and tell me which is a better investment"
Assistant: [Runs comparative analysis across multiple metrics]
```

### Investment Advice
```
User: "I'm a conservative investor with a medium portfolio. Should I invest in NVDA for long-term?"
Assistant: [Provides personalized recommendation based on risk profile]
```

### Market Overview
```
User: "How is the tech sector performing this month?"
Assistant: [Analyzes market trends and sector performance]
```

### News Sentiment
```
User: "What's the recent news sentiment around Tesla?"
Assistant: [Analyzes recent news and market sentiment]
```

## 🏗️ Architecture

### Core Components

1. **`chat_app.py`**: Main Streamlit application with UI and orchestration
2. **`bedrock_converse.py`**: AWS Bedrock Converse API integration
3. **`chat_agents.py`**: CrewAI agents for specialized analysis
4. **`chat_function_schemas.py`**: Function definitions for Converse API
5. **`test_chat_app.py`**: Comprehensive testing suite

### Function Calling Flow

```
User Input → Bedrock Converse API → Function Detection → Agent Execution → Results → Response
```

1. User types natural language query
2. Bedrock Converse API parses intent and identifies required functions
3. Functions are executed using specialized CrewAI agents
4. Results are synthesized and returned to the user
5. Conversation continues with full context

### Agent Specialization

- **Technical Analyst**: Chart patterns, indicators, trading signals
- **Fundamental Analyst**: Financial ratios, company health, valuation
- **News Analyst**: Sentiment analysis, market impact assessment
- **Investment Advisor**: Risk assessment, portfolio recommendations

## 🔧 Configuration

### AWS Bedrock Models

Supported models (configurable in sidebar):
- `anthropic.claude-3-5-sonnet-20241022-v2:0` (Default - Best performance)
- `anthropic.claude-3-5-sonnet-20240620-v1:0`
- `anthropic.claude-3-5-haiku-20241022-v1:0` (Faster, lower cost)
- `anthropic.claude-3-sonnet-20240229-v1:0`
- `anthropic.claude-3-haiku-20240307-v1:0`

### Chat Parameters

- **Temperature**: Controls response creativity (0.0-1.0)
- **Max Tokens**: Maximum response length (500-4000)
- **Model Selection**: Choose optimal model for your use case

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_chat_app.py
```

The test suite validates:
- ✅ Module imports and dependencies
- ✅ Function schema definitions
- ✅ Bedrock client functionality
- ✅ CrewAI agent creation
- ✅ AWS credentials detection
- ✅ Streamlit app structure

## 🔒 Security & Privacy

- **Credentials**: AWS credentials never stored permanently
- **Data**: No conversation data stored on external servers
- **Privacy**: All analysis runs in your AWS environment
- **Export**: Conversation history can be exported locally

## 🎛️ Advanced Features

### Conversation Management
- Persistent conversation history within session
- Context-aware follow-up questions
- Conversation export in JSON format
- Clear conversation functionality

### Function Validation
- Automatic parameter validation
- Error handling and user feedback
- Graceful degradation for missing data
- Comprehensive logging

### Multi-Agent Coordination
- Intelligent agent selection based on query type
- Parallel execution for complex analyses
- Result synthesis across multiple agents
- Contextual task dependencies

## 🚨 Troubleshooting

### Common Issues

**AWS Credentials Not Found**
- Verify credentials are set correctly
- Check AWS region supports Bedrock
- Ensure Bedrock model access is enabled

**Function Execution Errors**
- Check internet connectivity for data fetching
- Verify ticker symbols are valid
- Review AWS Bedrock service limits

**Performance Issues**
- Use Claude Haiku for faster responses
- Reduce max_tokens for quicker generation
- Clear conversation history if it becomes too long

### Getting Help

1. Run `python test_chat_app.py` to diagnose issues
2. Check AWS Bedrock console for model access
3. Verify all dependencies are installed correctly
4. Review Streamlit logs for detailed error messages

## 🔮 Future Enhancements

- **Portfolio Management**: Track and analyze entire portfolios
- **Real-time Alerts**: Set up price and news alerts
- **Advanced Charting**: Interactive technical analysis charts
- **Custom Agents**: User-defined analysis agents
- **API Integration**: RESTful API for programmatic access
- **Mobile Optimization**: Responsive design for mobile devices

## 📄 License

This project follows the same license as the main FinMAS project.

---

**Ready to start chatting with your AI financial advisor?**

```bash
streamlit run chat_app.py
```

Ask me anything about stocks, markets, or investments! 🚀📈
