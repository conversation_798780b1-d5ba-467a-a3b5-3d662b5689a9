"""
Main Application Orchestrator for FinMAS Enhanced

This module coordinates all components to ensure complete agent execution
and provides the main entry point for the enhanced financial analysis system.
"""

import streamlit as st
import sys
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
import time

# Add the parent directory to the path to import finmas modules
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))
sys.path.insert(0, str(parent_dir / "agentic-llm-for-better-results"))

try:
    from finmas.data.market import StockFundamentalsTool, TechnicalAnalysisTool
    from finmas.data.news.query_engine import get_news_query_engine
    from finmas.data.sec.query_engine import get_sec_query_engine
    from finmas.constants import defaults
    from edgartools import Filing, get_filings
except ImportError as e:
    print(f"Warning: Could not import some FinMAS modules: {e}")
    print("Some features may be limited. Continuing with basic functionality...")
    # Create mock classes for missing imports
    class StockFundamentalsTool:
        def __init__(self): pass
    class TechnicalAnalysisTool:
        def __init__(self): pass
    defaults = {"similarity_top_k": 5}

from utils.logging_utils import get_logger, setup_logging
from utils.bedrock_utils import EnhancedBedrockClient
from utils.crewai_utils import EnhancedFinancialCrew, CrewExecutionResult
from ui.streamlit_ui import FinMASStreamlitApp


class FinMASOrchestrator:
    """Main orchestrator for FinMAS Enhanced system"""
    
    def __init__(self):
        """Initialize the orchestrator"""
        # Setup logging
        self.logger = setup_logging()
        
        # Initialize components
        self.bedrock_client = None
        self.streamlit_app = FinMASStreamlitApp()
        
        self.logger.logger.info("FinMAS Enhanced Orchestrator initialized")
    
    def initialize_bedrock_client(
        self,
        model_id: str,
        temperature: float = 0.1,
        max_tokens: int = 2000,
        region_name: str = "us-east-1"
    ) -> EnhancedBedrockClient:
        """Initialize or update Bedrock client with specified parameters"""
        try:
            self.bedrock_client = EnhancedBedrockClient(
                model_id=model_id,
                region_name=region_name,
                max_retries=3,
                retry_delay=1.0
            )
            
            self.logger.logger.info(f"Bedrock client initialized with model: {model_id}")
            return self.bedrock_client
            
        except Exception as e:
            self.logger.logger.error(f"Failed to initialize Bedrock client: {str(e)}")
            st.error(f"Failed to initialize AWS Bedrock client: {str(e)}")
            raise
    
    def get_financial_tools(self, ticker: str) -> Dict[str, List[Any]]:
        """Get financial analysis tools for the specified ticker"""
        try:
            # Market data tools
            fundamental_tool = StockFundamentalsTool()
            technical_tool = TechnicalAnalysisTool()
            
            tools = {
                "fundamental": [fundamental_tool],
                "technical": [technical_tool],
                "news": [],  # Will be populated if news data is available
                "sec": []    # Will be populated if SEC data is available
            }
            
            self.logger.logger.info(f"Financial tools prepared for {ticker}")
            return tools
            
        except Exception as e:
            self.logger.logger.error(f"Failed to prepare financial tools: {str(e)}")
            st.error(f"Failed to prepare financial tools: {str(e)}")
            return {"fundamental": [], "technical": [], "news": [], "sec": []}
    
    def get_recent_sec_filing(self, ticker: str) -> Optional[Filing]:
        """Get the most recent SEC filing for the ticker"""
        try:
            # Get recent 10-K or 10-Q filings
            filings = get_filings(
                ticker=ticker,
                form=["10-K", "10-Q"],
                limit=5
            )
            
            if filings:
                latest_filing = filings[0]  # Most recent filing
                self.logger.logger.info(f"Found SEC filing for {ticker}: {latest_filing.form} from {latest_filing.filing_date}")
                return latest_filing
            else:
                self.logger.logger.warning(f"No recent SEC filings found for {ticker}")
                return None
                
        except Exception as e:
            self.logger.logger.error(f"Failed to get SEC filing for {ticker}: {str(e)}")
            return None
    
    @get_logger().log_function_call
    def run_complete_analysis(
        self,
        ticker: str,
        model_id: str,
        temperature: float = 0.1,
        max_tokens: int = 2000,
        components: Optional[Dict[str, bool]] = None
    ) -> CrewExecutionResult:
        """
        Run complete financial analysis for the specified ticker
        
        Args:
            ticker: Stock ticker symbol
            model_id: Bedrock model ID to use
            temperature: Model temperature
            max_tokens: Maximum tokens per request
            components: Dict specifying which analysis components to run
            
        Returns:
            CrewExecutionResult with complete analysis
        """
        if components is None:
            components = {
                "technical": True,
                "fundamental": True,
                "news": True,
                "recommendation": True
            }
        
        self.logger.logger.info(f"Starting complete analysis for {ticker}")
        
        # Initialize Bedrock client
        self.initialize_bedrock_client(model_id, temperature, max_tokens)
        
        # Get financial tools
        tools = self.get_financial_tools(ticker)
        
        # Try to get SEC filing data
        sec_filing = self.get_recent_sec_filing(ticker)
        if sec_filing:
            # Add SEC tools if filing is available
            try:
                sec_query_engine, _ = get_sec_query_engine(
                    ticker=ticker,
                    llm_provider="bedrock",
                    llm_model=model_id,
                    embedding_model="amazon.titan-embed-text-v1",
                    filing=sec_filing,
                    method="section:mda",
                    temperature=temperature,
                    max_tokens=max_tokens,
                    similarity_top_k=defaults["similarity_top_k"]
                )
                
                from crewai_tools import LlamaIndexTool
                sec_tool = LlamaIndexTool.from_query_engine(
                    sec_query_engine,
                    name=f"{sec_filing.form} SEC Filing Query Tool for {ticker}",
                    description=f"Use this tool to search and analyze the {sec_filing.form} SEC filing"
                )
                tools["sec"] = [sec_tool]
                
                self.logger.logger.info(f"SEC tools added for {ticker}")
                
            except Exception as e:
                self.logger.logger.warning(f"Failed to setup SEC tools: {str(e)}")
        
        # Initialize and run the enhanced crew
        try:
            crew = EnhancedFinancialCrew(
                ticker=ticker,
                bedrock_client=self.bedrock_client,
                tools=tools
            )
            
            # Execute complete analysis
            result = crew.execute_complete_analysis()
            
            self.logger.logger.info(
                f"Analysis completed for {ticker} - Success: {result.success}, "
                f"Cost: ${result.total_cost:.4f}, Duration: {result.total_duration_ms:.2f}ms"
            )
            
            return result
            
        except Exception as e:
            self.logger.logger.error(f"Analysis failed for {ticker}: {str(e)}")
            raise
    
    def run_streamlit_app(self):
        """Run the main Streamlit application"""
        try:
            # Run the Streamlit UI
            action = self.streamlit_app.run()
            
            # Handle analysis request
            if action and action.get('action') == 'run_analysis':
                ticker = action['ticker']
                model_id = action['model']
                temperature = action['temperature']
                max_tokens = action['max_tokens']
                components = action['components']
                
                # Set analysis in progress
                st.session_state.analysis_in_progress = True
                
                # Show progress
                with st.spinner(f"Running comprehensive analysis for {ticker}..."):
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    try:
                        # Update progress
                        progress_bar.progress(10)
                        status_text.text("Initializing Bedrock client...")
                        
                        # Run analysis
                        progress_bar.progress(30)
                        status_text.text("Executing financial agents...")
                        
                        result = self.run_complete_analysis(
                            ticker=ticker,
                            model_id=model_id,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            components=components
                        )
                        
                        progress_bar.progress(90)
                        status_text.text("Finalizing results...")
                        
                        # Store results
                        st.session_state.analysis_results.append(result)
                        
                        progress_bar.progress(100)
                        status_text.text("Analysis complete!")
                        
                        # Clear progress indicators
                        time.sleep(1)
                        progress_bar.empty()
                        status_text.empty()
                        
                        # Show success message
                        st.success(f"✅ Analysis completed for {ticker}!")
                        
                        # Save session data
                        self.logger.save_session_data()
                        
                    except Exception as e:
                        progress_bar.empty()
                        status_text.empty()
                        st.error(f"❌ Analysis failed: {str(e)}")
                        self.logger.logger.error(f"Analysis failed: {str(e)}")
                    
                    finally:
                        st.session_state.analysis_in_progress = False
                        st.rerun()
            
        except Exception as e:
            self.logger.logger.error(f"Streamlit app error: {str(e)}")
            st.error(f"Application error: {str(e)}")


def main():
    """Main entry point for FinMAS Enhanced"""
    try:
        # Initialize and run the orchestrator
        orchestrator = FinMASOrchestrator()
        orchestrator.run_streamlit_app()
        
    except Exception as e:
        st.error(f"Failed to start FinMAS Enhanced: {str(e)}")
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
