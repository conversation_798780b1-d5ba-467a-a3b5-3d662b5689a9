#!/usr/bin/env python3
"""
FinMAS Enhanced - Demo Application

A simplified demo version that showcases the enhanced architecture and logging
without requiring the full FinMAS dependencies.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time
import json
import sys
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import our enhanced utilities
from utils.logging_utils import get_logger, setup_logging
from config import BEDROCK_CONFIG, AVAILABLE_MODELS, MODEL_PRICING

# Setup page config
st.set_page_config(
    page_title="FinMAS Enhanced - Demo",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize logger
@st.cache_resource
def init_logger():
    return setup_logging()

logger = init_logger()

def render_header():
    """Render the application header with cost tracking"""
    st.title("🚀 FinMAS Enhanced - Demo")
    st.subheader("Financial Multi-Agent System with Comprehensive Analytics")
    
    # Cost tracking display
    cost_summary = logger.cost_tracker.get_session_summary()
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Session Cost", f"${cost_summary['total_cost']:.4f}")
    with col2:
        st.metric("Total API Calls", cost_summary['total_calls'])
    with col3:
        st.metric("Total Tokens", f"{cost_summary['total_tokens']:,}")
    with col4:
        avg_cost = cost_summary['average_cost_per_call']
        st.metric("Avg Cost/Call", f"${avg_cost:.4f}")

def render_sidebar():
    """Render the sidebar with controls"""
    with st.sidebar:
        st.header("Analysis Settings")
        
        # Ticker input
        ticker = st.text_input(
            "Stock Ticker",
            value="AAPL",
            placeholder="e.g., AAPL, MSFT, GOOGL"
        ).upper()
        
        # Model selection
        model_options = [model["id"] for model in AVAILABLE_MODELS]
        selected_model = st.selectbox("Bedrock Model", model_options)
        
        # Analysis parameters
        st.subheader("Analysis Parameters")
        temperature = st.slider("Temperature", 0.0, 1.0, 0.1, 0.1)
        max_tokens = st.slider("Max Tokens", 500, 4000, 2000, 100)
        
        # Analysis type selection
        st.subheader("Analysis Components")
        run_technical = st.checkbox("Technical Analysis", True)
        run_fundamental = st.checkbox("Fundamental Analysis", True)
        run_news = st.checkbox("News Analysis", True)
        run_recommendation = st.checkbox("Investment Recommendation", True)
        
        # Action buttons
        st.subheader("Actions")
        
        if st.button("🔍 Run Demo Analysis"):
            return {
                'action': 'run_analysis',
                'ticker': ticker,
                'model': selected_model,
                'temperature': temperature,
                'max_tokens': max_tokens,
                'components': {
                    'technical': run_technical,
                    'fundamental': run_fundamental,
                    'news': run_news,
                    'recommendation': run_recommendation
                }
            }
        
        if st.button("📊 View Cost Analytics"):
            return {'action': 'show_analytics'}
        
        if st.button("🗑️ Clear Session"):
            return {'action': 'clear_session'}
    
    return None

def simulate_agent_analysis(agent_name: str, ticker: str, model_id: str, duration_ms: float = 2000):
    """Simulate an agent analysis with cost tracking"""
    
    # Simulate token usage
    input_tokens = 150 + int(duration_ms / 10)  # Simulate based on complexity
    output_tokens = 300 + int(duration_ms / 5)
    
    # Calculate cost
    input_cost, output_cost, total_cost = logger.cost_tracker.calculate_cost(
        model_id, input_tokens, output_tokens
    )
    
    # Log the API call
    api_call = logger.cost_tracker.log_api_call(
        model_id=model_id,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        function_name=f"{agent_name}_analysis",
        duration_ms=duration_ms,
        success=True
    )
    
    # Generate mock analysis based on agent type
    if agent_name == "Technical Analyst":
        analysis = f"""
## Technical Analysis for {ticker}

**Current Price Action:**
- Current Price: $199.01 (+1.24%)
- 52-Week Range: $163.10 - $259.47
- Trading at 76% of 52-week high

**Technical Indicators:**
- RSI (14): 65.2 (Neutral to Bullish)
- MACD: Bullish crossover confirmed
- 20-day SMA: $195.50 (Support)
- 50-day SMA: $188.75 (Strong Support)
- 200-day SMA: $175.25 (Long-term Support)

**Support & Resistance:**
- Immediate Support: $195.00
- Strong Support: $188.00
- Immediate Resistance: $205.00
- Strong Resistance: $220.00

**Trading Recommendation:**
- **Signal:** BUY on pullback to $195-197 range
- **Target:** $215-220 (8-10% upside)
- **Stop Loss:** $185 (7% downside)
- **Time Horizon:** 4-6 weeks
        """
    
    elif agent_name == "Fundamental Analyst":
        analysis = f"""
## Fundamental Analysis for {ticker}

**Financial Health:**
- Market Cap: $2.97 trillion
- P/E Ratio: 30.95x (Premium valuation)
- Revenue (TTM): $394.3B (****% YoY)
- Net Income: $97.0B (****% YoY)
- Free Cash Flow: $84.7B (Strong generation)

**Key Metrics:**
- Gross Margin: 45.6% (Industry-leading)
- Operating Margin: 29.8% (Excellent efficiency)
- ROE: 147.4% (Outstanding returns)
- Debt-to-Equity: 1.73 (Manageable leverage)
- Current Ratio: 1.07 (Adequate liquidity)

**Growth Prospects:**
- Services Revenue: $85.2B (+16.3% YoY)
- iPhone Revenue: $200.6B (-2.4% YoY)
- Emerging Markets Growth: +12% YoY
- R&D Investment: $29.9B (+14.2% YoY)

**Investment Recommendation:**
- **Rating:** BUY
- **Fair Value:** $225-240
- **Upside Potential:** 13-20%
- **Investment Horizon:** 12-18 months
        """
    
    elif agent_name == "News Analyst":
        analysis = f"""
## News & Sentiment Analysis for {ticker}

**Recent Developments:**
- Q4 2024 earnings beat expectations (+$0.15 EPS)
- New AI chip announcement driving optimism
- Services segment showing strong momentum
- China market recovery signs emerging

**Market Sentiment:**
- Analyst Sentiment: 68% Buy, 25% Hold, 7% Sell
- Social Media Sentiment: 72% Positive
- Options Flow: Bullish (Call/Put ratio: 1.8)
- Institutional Activity: Net buying (+$2.1B)

**Key Catalysts:**
- **Upcoming:** WWDC 2025 (June) - AI announcements expected
- **Q1 2025 Earnings:** March 15, 2025
- **Product Launches:** New iPhone models (September)
- **Regulatory:** EU compliance costs manageable

**Risk Factors:**
- China trade tensions (Medium risk)
- Smartphone market saturation (Low risk)
- Regulatory scrutiny (Low-Medium risk)
- Supply chain disruptions (Low risk)

**Sentiment Score:** 7.2/10 (Bullish)
        """
    
    else:  # Investment Advisor
        analysis = f"""
## Investment Recommendation for {ticker}

**Executive Summary:**
Based on comprehensive technical, fundamental, and sentiment analysis, {ticker} presents a compelling investment opportunity with balanced risk-reward profile.

**Investment Rating:** **BUY**
**Confidence Level:** 85%

**Price Targets:**
- 6-Month Target: $215 (+8% upside)
- 12-Month Target: $235 (+18% upside)
- Bear Case: $175 (-12% downside)
- Bull Case: $260 (+31% upside)

**Key Investment Thesis:**
1. **Strong Fundamentals:** Market-leading margins and cash generation
2. **Technical Setup:** Bullish momentum with clear support levels
3. **Positive Catalysts:** AI integration and services growth
4. **Valuation:** Reasonable premium for quality and growth

**Portfolio Allocation:**
- **Conservative Investors:** 3-5% allocation
- **Moderate Investors:** 5-8% allocation
- **Aggressive Investors:** 8-12% allocation

**Action Plan:**
1. **Entry Strategy:** Dollar-cost average over 4-6 weeks
2. **Optimal Entry:** $195-200 range on any pullback
3. **Stop Loss:** $185 (trailing stop recommended)
4. **Profit Taking:** 50% at $220, 50% at $235
5. **Review Schedule:** Monthly reassessment

**Risk Management:**
- Position size should not exceed 10% of portfolio
- Monitor quarterly earnings closely
- Watch for changes in China trade dynamics
- Set alerts for technical breakdown below $185
        """
    
    return {
        'agent_name': agent_name,
        'analysis': analysis,
        'cost': total_cost,
        'tokens': input_tokens + output_tokens,
        'duration_ms': duration_ms
    }

def run_demo_analysis(ticker: str, model_id: str, components: dict):
    """Run a demo analysis simulation"""
    
    st.header(f"📈 Demo Analysis Results for {ticker}")
    
    # Progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    agents = []
    if components['technical']:
        agents.append("Technical Analyst")
    if components['fundamental']:
        agents.append("Fundamental Analyst")
    if components['news']:
        agents.append("News Analyst")
    if components['recommendation']:
        agents.append("Investment Advisor")
    
    results = []
    total_cost = 0.0
    total_tokens = 0
    
    for i, agent in enumerate(agents):
        status_text.text(f"Running {agent} analysis...")
        progress_bar.progress((i + 1) / len(agents))
        
        # Simulate processing time
        time.sleep(1)
        
        # Run simulated analysis
        result = simulate_agent_analysis(agent, ticker, model_id)
        results.append(result)
        
        total_cost += result['cost']
        total_tokens += result['tokens']
    
    progress_bar.progress(1.0)
    status_text.text("Analysis complete!")
    
    # Clear progress indicators
    time.sleep(0.5)
    progress_bar.empty()
    status_text.empty()
    
    # Display results
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Cost", f"${total_cost:.4f}")
    with col2:
        st.metric("Total Tokens", f"{total_tokens:,}")
    with col3:
        st.metric("Agents Executed", len(results))
    
    # Create tabs for each agent result
    if results:
        tabs = st.tabs([result['agent_name'] for result in results])
        
        for i, result in enumerate(results):
            with tabs[i]:
                # Agent metrics
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Cost", f"${result['cost']:.4f}")
                with col2:
                    st.metric("Tokens", f"{result['tokens']:,}")
                with col3:
                    st.metric("Duration", f"{result['duration_ms']:.0f}ms")
                
                # Agent analysis
                st.markdown(result['analysis'])

def render_cost_analytics():
    """Render cost analytics dashboard"""
    st.header("💰 Cost Analytics Dashboard")
    
    cost_summary = logger.cost_tracker.get_session_summary()
    api_calls = logger.cost_tracker.api_calls
    
    if not api_calls:
        st.info("No API calls recorded yet. Run a demo analysis to see cost data.")
        return
    
    # Cost overview
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Session Cost", f"${cost_summary['total_cost']:.4f}")
    with col2:
        st.metric("Total API Calls", cost_summary['total_calls'])
    with col3:
        st.metric("Total Tokens", f"{cost_summary['total_tokens']:,}")
    with col4:
        st.metric("Average Cost per Call", f"${cost_summary['average_cost_per_call']:.4f}")
    
    # API calls timeline
    if api_calls:
        calls_df = pd.DataFrame([
            {
                "Timestamp": call.timestamp,
                "Function": call.function_name,
                "Input Tokens": call.input_tokens,
                "Output Tokens": call.output_tokens,
                "Total Cost": call.total_cost,
                "Duration (ms)": call.duration_ms
            }
            for call in api_calls
        ])
        
        # Convert timestamp to datetime
        calls_df['Timestamp'] = pd.to_datetime(calls_df['Timestamp'])
        
        # Cost over time
        fig = px.line(calls_df, x='Timestamp', y='Total Cost', title="Cost Over Time")
        st.plotly_chart(fig, use_container_width=True)
        
        # Detailed calls table
        st.subheader("Detailed API Calls")
        st.dataframe(calls_df, use_container_width=True)

def main():
    """Main application"""
    render_header()
    
    # Handle sidebar actions
    action = render_sidebar()
    
    if action:
        if action['action'] == 'clear_session':
            # Clear session data
            logger.cost_tracker.total_cost = 0.0
            logger.cost_tracker.api_calls = []
            logger.performance_monitor.function_calls = []
            logger.performance_monitor.agent_executions = []
            st.success("Session cleared successfully!")
            st.rerun()
            
        elif action['action'] == 'show_analytics':
            render_cost_analytics()
            
        elif action['action'] == 'run_analysis':
            run_demo_analysis(
                action['ticker'],
                action['model'],
                action['components']
            )
    else:
        # Welcome message
        st.info("""
        👋 **Welcome to FinMAS Enhanced Demo!**
        
        This demo showcases the enhanced architecture with:
        - ✅ Comprehensive cost tracking and analytics
        - ✅ Modular agent-based analysis
        - ✅ Real-time performance monitoring
        - ✅ Professional financial analysis output
        
        **To get started:**
        1. Enter a stock ticker in the sidebar
        2. Select your analysis parameters
        3. Click "Run Demo Analysis"
        4. View comprehensive results with cost tracking
        
        **Note:** This is a demonstration version with simulated analysis. 
        The full version integrates with AWS Bedrock and real financial data sources.
        """)

if __name__ == "__main__":
    main()
