#!/usr/bin/env python3
"""
FinMAS Enhanced - Conversational Chat Interface

A natural language conversational interface for financial analysis
using AWS Bedrock Converse API with function calling.
"""

import streamlit as st
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import our enhanced utilities
from utils.conversational_bedrock import ConversationalBedrockClient
from utils.logging_utils import get_logger, setup_logging
from utils.conversation_manager import ConversationManager
from config import BEDROCK_CONFIG, AVAILABLE_MODELS

# Setup page config
st.set_page_config(
    page_title="FinMAS Enhanced - Chat",
    page_icon="💬",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for better chat interface
st.markdown("""
<style>
    .stChatMessage {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .cost-info {
        font-size: 0.8rem;
        color: #666;
        margin-top: 0.5rem;
        padding: 0.25rem 0.5rem;
        background-color: #f0f2f6;
        border-radius: 0.25rem;
    }
    
    .context-bar {
        background-color: #f8f9fa;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: #495057;
    }
    
    .welcome-message {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'bedrock_client' not in st.session_state:
    st.session_state.bedrock_client = None
if 'chat_initialized' not in st.session_state:
    st.session_state.chat_initialized = False
if 'processing' not in st.session_state:
    st.session_state.processing = False

@st.cache_resource
def init_logger():
    """Initialize logger"""
    return setup_logging()

def init_bedrock_client(model_id: str, temperature: float = 0.1, max_tokens: int = 3000):
    """Initialize Bedrock client"""
    try:
        client = ConversationalBedrockClient(
            model_id=model_id,
            temperature=temperature,
            max_tokens=max_tokens
        )
        return client, None
    except Exception as e:
        return None, str(e)

def render_header():
    """Render the application header"""
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.title("💬 FinMAS Enhanced - Chat")
        st.caption("Natural Language Financial Analysis Assistant")
    
    with col2:
        # Settings expander
        with st.expander("⚙️ Settings", expanded=False):
            # Model selection
            model_options = [model["id"] for model in AVAILABLE_MODELS]
            selected_model = st.selectbox(
                "Model", 
                model_options, 
                index=0,
                key="model_selector"
            )
            
            # Parameters
            temperature = st.slider("Temperature", 0.0, 1.0, 0.1, 0.1, key="temp_slider")
            max_tokens = st.slider("Max Tokens", 1000, 4000, 3000, 100, key="tokens_slider")
            
            # Initialize/Update client button
            if st.button("🔄 Update Settings"):
                with st.spinner("Updating settings..."):
                    client, error = init_bedrock_client(selected_model, temperature, max_tokens)
                    if client:
                        st.session_state.bedrock_client = client
                        st.session_state.chat_initialized = True
                        st.success("Settings updated!")
                        st.rerun()
                    else:
                        st.error(f"Failed to initialize client: {error}")

def render_cost_tracking():
    """Render real-time cost tracking with enhanced chat interface styling"""
    if st.session_state.bedrock_client:
        logger = get_logger()
        cost_summary = logger.cost_tracker.get_session_summary()

        # Create a more compact, chat-friendly cost display
        col1, col2, col3, col4, col5 = st.columns(5)

        with col1:
            cost_color = "🟢" if cost_summary['total_cost'] < 1.0 else "🟡" if cost_summary['total_cost'] < 5.0 else "🔴"
            st.metric("💰 Cost", f"${cost_summary['total_cost']:.4f}", delta=cost_color)

        with col2:
            st.metric("📞 Calls", cost_summary['total_calls'])

        with col3:
            st.metric("🔤 Tokens", f"{cost_summary['total_tokens']:,}")

        with col4:
            efficiency = cost_summary['total_tokens'] / max(cost_summary['total_cost'], 0.0001)
            st.metric("⚡ Efficiency", f"{efficiency:.0f} tok/$")

        with col5:
            # Show session duration
            if st.session_state.bedrock_client.conversation_manager.context.session_start_time:
                from datetime import datetime
                start_time = datetime.fromisoformat(st.session_state.bedrock_client.conversation_manager.context.session_start_time)
                duration = datetime.now() - start_time
                duration_mins = int(duration.total_seconds() / 60)
                st.metric("⏱️ Session", f"{duration_mins}m")

def render_context_bar():
    """Render conversation context"""
    if st.session_state.bedrock_client:
        context_summary = st.session_state.bedrock_client.conversation_manager.get_context_summary()
        st.markdown(f'<div class="context-bar">📊 {context_summary}</div>', unsafe_allow_html=True)

def render_chat_interface():
    """Render the main chat interface"""
    
    # Initialize client if not done
    if not st.session_state.chat_initialized:
        st.markdown("""
        <div class="welcome-message">
            <h3>🚀 Welcome to FinMAS Enhanced Chat!</h3>
            <p>I'm your AI financial analyst assistant. Click "Update Settings" above to get started, then ask me anything about stocks!</p>
            <p><strong>Try asking:</strong></p>
            <ul>
                <li>"Analyze AAPL stock"</li>
                <li>"What's the technical outlook for Microsoft?"</li>
                <li>"Compare AAPL vs MSFT"</li>
                <li>"Should I invest in Tesla?"</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
        return
    
    # Get conversation history
    chat_history = st.session_state.bedrock_client.get_conversation_history()
    
    # Display chat messages
    for message in chat_history:
        display_msg = st.session_state.bedrock_client.conversation_manager.format_message_for_display(message)
        
        with st.chat_message(display_msg["role"]):
            st.markdown(display_msg["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me about stocks... (e.g., 'Analyze AAPL' or 'What's the outlook for Microsoft?')", disabled=st.session_state.processing):
        
        # Display user message immediately
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Process the message
        st.session_state.processing = True
        
        with st.chat_message("assistant"):
            # Show thinking indicator
            thinking_placeholder = st.empty()
            thinking_placeholder.markdown("🤔 Analyzing your request...")
            
            try:
                # Process user message
                response_text, cost_info = st.session_state.bedrock_client.process_user_message(prompt)
                
                # Clear thinking indicator and show response
                thinking_placeholder.empty()
                st.markdown(response_text)
                
                # Show cost info if available
                if cost_info and cost_info.get('total_cost', 0) > 0:
                    cost_text = f"💰 **Cost:** ${cost_info['total_cost']:.4f} | "
                    cost_text += f"Tokens: {cost_info['total_tokens']:,} | "
                    cost_text += f"Duration: {cost_info['duration_ms']:.0f}ms"
                    
                    if cost_info.get('function_calls', 0) > 0:
                        cost_text += f" | Functions: {cost_info['function_calls']}"
                    
                    st.caption(cost_text)
                
            except Exception as e:
                thinking_placeholder.empty()
                st.error(f"Sorry, I encountered an error: {str(e)}")
                st.caption("Please try again or rephrase your question.")
        
        st.session_state.processing = False
        st.rerun()

def render_sidebar():
    """Render minimal sidebar with quick actions"""
    with st.sidebar:
        st.header("Quick Actions")
        
        if st.button("🗑️ Clear Chat"):
            if st.session_state.bedrock_client:
                st.session_state.bedrock_client.clear_conversation()
                st.success("Chat cleared!")
                st.rerun()
        
        if st.button("💰 Cost Summary"):
            if st.session_state.bedrock_client:
                logger = get_logger()
                cost_summary = logger.cost_tracker.get_session_summary()
                
                st.subheader("Session Costs")
                st.metric("Total Cost", f"${cost_summary['total_cost']:.4f}")
                st.metric("API Calls", cost_summary['total_calls'])
                st.metric("Total Tokens", f"{cost_summary['total_tokens']:,}")
                
                if cost_summary.get('model_breakdown'):
                    st.subheader("Model Breakdown")
                    for model, cost in cost_summary['model_breakdown'].items():
                        st.write(f"**{model.split('.')[-1]}:** ${cost:.4f}")
        
        st.markdown("---")
        st.subheader("💡 Tips")
        st.markdown("""
        **Try these commands:**
        - "Analyze [TICKER]"
        - "Technical analysis of [TICKER]"
        - "Compare [TICKER1] vs [TICKER2]"
        - "Should I buy [TICKER]?"
        - "What's my session cost?"
        - "Clear my data"
        """)
        
        st.markdown("---")
        st.caption("FinMAS Enhanced v2.0")

def main():
    """Main application"""
    # Initialize logger
    logger = init_logger()
    
    # Render header
    render_header()
    
    # Render cost tracking
    render_cost_tracking()
    
    # Render context bar
    render_context_bar()
    
    # Main content area
    col1, col2 = st.columns([4, 1])
    
    with col1:
        # Render chat interface
        render_chat_interface()
    
    with col2:
        # Render sidebar
        render_sidebar()

if __name__ == "__main__":
    main()
