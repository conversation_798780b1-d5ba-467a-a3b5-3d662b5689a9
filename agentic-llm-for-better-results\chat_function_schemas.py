"""
Function calling schemas for AWS Bedrock Converse API

This module defines the function schemas that the Converse API can use to parse
user input and determine which analysis operations to perform.
"""

from typing import Dict, List, Any, Optional
from enum import Enum


class AnalysisType(str, Enum):
    """Types of analysis that can be performed."""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    NEWS = "news"
    SEC_FILING = "sec_filing"
    COMBINED = "combined"


class TimeFrame(str, Enum):
    """Time frames for analysis."""
    ONE_WEEK = "1w"
    ONE_MONTH = "1mo"
    THREE_MONTHS = "3mo"
    SIX_MONTHS = "6mo"
    ONE_YEAR = "1y"
    TWO_YEARS = "2y"


class FilingType(str, Enum):
    """SEC filing types."""
    TEN_K = "10-K"
    TEN_Q = "10-Q"
    EIGHT_K = "8-K"
    PROXY = "DEF 14A"
    ALL = "all"


# Function schemas for AWS Bedrock Converse API
FUNCTION_SCHEMAS = [
    {
        "toolSpec": {
            "name": "analyze_stock",
            "description": "Perform comprehensive stock analysis including technical, fundamental, news, and SEC filing analysis",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "ticker": {
                            "type": "string",
                            "description": "Stock ticker symbol (e.g., AAPL, MSFT, GOOGL)"
                        },
                        "analysis_types": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["technical", "fundamental", "news", "sec_filing", "combined"]
                            },
                            "description": "Types of analysis to perform"
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["1w", "1mo", "3mo", "6mo", "1y", "2y"],
                            "description": "Time frame for the analysis",
                            "default": "6mo"
                        },
                        "include_charts": {
                            "type": "boolean",
                            "description": "Whether to include charts and visualizations",
                            "default": True
                        }
                    },
                    "required": ["ticker", "analysis_types"]
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "get_technical_analysis",
            "description": "Perform technical analysis on a stock including price trends, indicators, and patterns",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "ticker": {
                            "type": "string",
                            "description": "Stock ticker symbol"
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["1w", "1mo", "3mo", "6mo", "1y", "2y"],
                            "description": "Time frame for technical analysis",
                            "default": "6mo"
                        },
                        "indicators": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["sma", "ema", "rsi", "macd", "bollinger", "volume", "all"]
                            },
                            "description": "Technical indicators to include",
                            "default": ["sma", "rsi", "volume"]
                        }
                    },
                    "required": ["ticker"]
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "get_fundamental_analysis",
            "description": "Perform fundamental analysis including financial metrics, ratios, and company health",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "ticker": {
                            "type": "string",
                            "description": "Stock ticker symbol"
                        },
                        "metrics": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["valuation", "profitability", "growth", "financial_health", "all"]
                            },
                            "description": "Fundamental metrics to analyze",
                            "default": ["all"]
                        },
                        "compare_to_peers": {
                            "type": "boolean",
                            "description": "Whether to compare metrics to industry peers",
                            "default": False
                        }
                    },
                    "required": ["ticker"]
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "get_news_sentiment",
            "description": "Analyze news sentiment and recent developments for a stock",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "ticker": {
                            "type": "string",
                            "description": "Stock ticker symbol"
                        },
                        "date_range": {
                            "type": "string",
                            "enum": ["1w", "1mo", "3mo", "6mo"],
                            "description": "Date range for news analysis",
                            "default": "1mo"
                        },
                        "max_articles": {
                            "type": "integer",
                            "description": "Maximum number of articles to analyze",
                            "default": 20,
                            "minimum": 5,
                            "maximum": 100
                        },
                        "sentiment_only": {
                            "type": "boolean",
                            "description": "Whether to return only sentiment analysis or full article summaries",
                            "default": False
                        }
                    },
                    "required": ["ticker"]
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "analyze_sec_filings",
            "description": "Analyze SEC filings for insights into company performance and risks",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "ticker": {
                            "type": "string",
                            "description": "Stock ticker symbol"
                        },
                        "filing_types": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["10-K", "10-Q", "8-K", "DEF 14A", "all"]
                            },
                            "description": "Types of SEC filings to analyze",
                            "default": ["10-K", "10-Q"]
                        },
                        "max_filings": {
                            "type": "integer",
                            "description": "Maximum number of recent filings to analyze",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 20
                        },
                        "focus_areas": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["financial_highlights", "risk_factors", "management_discussion", "business_overview", "all"]
                            },
                            "description": "Areas to focus on in the analysis",
                            "default": ["financial_highlights", "risk_factors"]
                        }
                    },
                    "required": ["ticker"]
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "compare_stocks",
            "description": "Compare multiple stocks across various metrics and analysis types",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "tickers": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            },
                            "description": "List of stock ticker symbols to compare",
                            "minItems": 2,
                            "maxItems": 5
                        },
                        "comparison_metrics": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "enum": ["technical", "fundamental", "news_sentiment", "performance", "all"]
                            },
                            "description": "Metrics to compare across stocks",
                            "default": ["fundamental", "performance"]
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["1mo", "3mo", "6mo", "1y", "2y"],
                            "description": "Time frame for comparison",
                            "default": "6mo"
                        }
                    },
                    "required": ["tickers"]
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "get_market_overview",
            "description": "Get an overview of market conditions and trends",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "sectors": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            },
                            "description": "Specific sectors to focus on (optional)",
                            "default": []
                        },
                        "include_indices": {
                            "type": "boolean",
                            "description": "Whether to include major market indices analysis",
                            "default": True
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["1w", "1mo", "3mo", "6mo", "1y"],
                            "description": "Time frame for market overview",
                            "default": "1mo"
                        }
                    },
                    "required": []
                }
            }
        }
    },
    {
        "toolSpec": {
            "name": "get_investment_recommendation",
            "description": "Generate investment recommendation based on comprehensive analysis",
            "inputSchema": {
                "json": {
                    "type": "object",
                    "properties": {
                        "ticker": {
                            "type": "string",
                            "description": "Stock ticker symbol"
                        },
                        "investment_horizon": {
                            "type": "string",
                            "enum": ["short_term", "medium_term", "long_term"],
                            "description": "Investment time horizon",
                            "default": "medium_term"
                        },
                        "risk_tolerance": {
                            "type": "string",
                            "enum": ["conservative", "moderate", "aggressive"],
                            "description": "Risk tolerance level",
                            "default": "moderate"
                        },
                        "portfolio_size": {
                            "type": "string",
                            "enum": ["small", "medium", "large"],
                            "description": "Portfolio size category",
                            "default": "medium"
                        }
                    },
                    "required": ["ticker"]
                }
            }
        }
    }
]


def get_function_schema_by_name(function_name: str) -> Optional[Dict[str, Any]]:
    """Get a specific function schema by name."""
    for schema in FUNCTION_SCHEMAS:
        if schema["toolSpec"]["name"] == function_name:
            return schema
    return None


def get_all_function_names() -> List[str]:
    """Get all available function names."""
    return [schema["toolSpec"]["name"] for schema in FUNCTION_SCHEMAS]


def validate_function_call(function_name: str, parameters: Dict[str, Any]) -> bool:
    """Validate a function call against its schema."""
    schema = get_function_schema_by_name(function_name)
    if not schema:
        return False
    
    # Basic validation - check required parameters
    required_params = schema["toolSpec"]["inputSchema"]["json"].get("required", [])
    for param in required_params:
        if param not in parameters:
            return False
    
    return True
