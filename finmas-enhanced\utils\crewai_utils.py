"""
Enhanced CrewAI Utilities for FinMAS

This module provides comprehensive CrewAI orchestration with:
- Complete agent execution tracking
- Result aggregation and synthesis
- Performance monitoring
- Cost tracking integration
"""

import time
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from crewai import Agent, Crew, Process, Task
from crewai_tools import LlamaIndexTool

from .logging_utils import get_logger
from .bedrock_utils import EnhancedBedrockClient


@dataclass
class AgentResult:
    """Represents the result of an agent execution"""
    agent_name: str
    task_name: str
    output: str
    execution_time_ms: float
    token_usage: Dict[str, int]
    cost: float
    success: bool
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class CrewExecutionResult:
    """Represents the complete result of a crew execution"""
    crew_name: str
    ticker: str
    start_time: str
    end_time: str
    total_duration_ms: float
    agent_results: List[AgentResult]
    final_output: str
    total_cost: float
    total_tokens: int
    success: bool
    error_message: Optional[str] = None


class EnhancedFinancialAgents:
    """Enhanced financial analysis agents with comprehensive tracking"""
    
    def __init__(self, bedrock_client: EnhancedBedrockClient, ticker: str):
        self.bedrock_client = bedrock_client
        self.ticker = ticker
        self.logger = get_logger()
        
        # Agent configurations
        self.agent_configs = {
            "technical_analyst": {
                "role": "Senior Technical Analyst",
                "goal": f"Analyze technical indicators and price patterns for {ticker} to identify trends and trading opportunities",
                "backstory": "You are an expert technical analyst with 15+ years of experience in market analysis. You specialize in chart patterns, technical indicators, and price action analysis.",
                "verbose": True,
                "allow_delegation": False
            },
            "fundamental_analyst": {
                "role": "Senior Fundamental Analyst", 
                "goal": f"Conduct comprehensive fundamental analysis of {ticker} including financial health, valuation, and growth prospects",
                "backstory": "You are a seasoned fundamental analyst with deep expertise in financial statement analysis, valuation models, and industry research. You focus on long-term investment value.",
                "verbose": True,
                "allow_delegation": False
            },
            "news_analyst": {
                "role": "Financial News Analyst",
                "goal": f"Analyze recent news and market sentiment for {ticker} to identify catalysts and risks",
                "backstory": "You are a financial news analyst who specializes in interpreting market news, earnings reports, and corporate announcements to assess their impact on stock performance.",
                "verbose": True,
                "allow_delegation": False
            },
            "investment_advisor": {
                "role": "Senior Investment Advisor",
                "goal": f"Synthesize all analysis to provide comprehensive investment recommendation for {ticker}",
                "backstory": "You are a senior investment advisor with 20+ years of experience. You excel at synthesizing complex financial analysis into clear, actionable investment recommendations.",
                "verbose": True,
                "allow_delegation": False
            }
        }
        
        # Task configurations
        self.task_configs = {
            "technical_analysis": {
                "description": f"""
                Conduct comprehensive technical analysis for {ticker}:
                1. Analyze current price trends and momentum
                2. Evaluate key technical indicators (RSI, MACD, Moving Averages)
                3. Identify support and resistance levels
                4. Assess volume patterns and market sentiment
                5. Provide short-term and medium-term outlook
                6. Include specific price targets and risk levels
                
                Provide detailed analysis with specific numbers and actionable insights.
                """,
                "expected_output": "Comprehensive technical analysis report with specific price levels, indicators, and trading recommendations"
            },
            "fundamental_analysis": {
                "description": f"""
                Perform in-depth fundamental analysis for {ticker}:
                1. Analyze financial statements and key metrics
                2. Evaluate revenue growth, profitability, and cash flow
                3. Assess debt levels and financial health
                4. Compare valuation metrics to industry peers
                5. Analyze business model and competitive advantages
                6. Provide specific revenue numbers, growth rates, and financial ratios
                7. Calculate intrinsic value and fair value estimates
                
                Include specific quantitative metrics and detailed financial analysis.
                """,
                "expected_output": "Detailed fundamental analysis with specific financial metrics, growth rates, and valuation assessment"
            },
            "news_analysis": {
                "description": f"""
                Analyze recent news and market sentiment for {ticker}:
                1. Review latest news articles and press releases
                2. Assess market sentiment and analyst opinions
                3. Identify key catalysts and risk factors
                4. Evaluate impact of recent earnings or announcements
                5. Analyze social media sentiment and retail investor interest
                6. Assess regulatory or industry developments
                
                Provide comprehensive sentiment analysis with specific examples and impact assessment.
                """,
                "expected_output": "Complete news and sentiment analysis with specific catalysts, risks, and market impact assessment"
            },
            "investment_recommendation": {
                "description": f"""
                Synthesize all analysis to create comprehensive investment recommendation for {ticker}:
                1. Integrate technical, fundamental, and news analysis
                2. Provide clear investment rating (Strong Buy/Buy/Hold/Sell/Strong Sell)
                3. Set specific price targets and time horizons
                4. Identify key risks and mitigation strategies
                5. Recommend position sizing and portfolio allocation
                6. Include specific entry and exit strategies
                7. Provide quantitative metrics supporting the recommendation
                
                Create a comprehensive investment thesis with specific actionable recommendations.
                """,
                "expected_output": "Complete investment recommendation with rating, price targets, risk assessment, and specific action plan"
            }
        }
    
    @get_logger().log_function_call
    def create_agent(self, agent_type: str, tools: List[Any]) -> Agent:
        """Create an enhanced agent with logging"""
        config = self.agent_configs[agent_type]
        
        # Create agent with Bedrock LLM
        agent = Agent(
            role=config["role"],
            goal=config["goal"],
            backstory=config["backstory"],
            verbose=config["verbose"],
            allow_delegation=config["allow_delegation"],
            llm=self.bedrock_client,  # Use our enhanced Bedrock client
            tools=tools,
            memory=True
        )
        
        self.logger.logger.info(f"Created {agent_type} agent for {self.ticker}")
        return agent
    
    @get_logger().log_function_call
    def create_task(self, task_type: str, agent: Agent, context: Optional[List[Task]] = None) -> Task:
        """Create an enhanced task with logging"""
        config = self.task_configs[task_type]
        
        task = Task(
            description=config["description"],
            expected_output=config["expected_output"],
            agent=agent,
            context=context or []
        )
        
        self.logger.logger.info(f"Created {task_type} task for {self.ticker}")
        return task
    
    @get_logger().log_function_call
    def execute_agent_task(
        self,
        agent: Agent,
        task: Task,
        agent_name: str,
        task_name: str
    ) -> AgentResult:
        """Execute a single agent task with comprehensive tracking"""
        start_time = time.time()
        start_timestamp = self.logger.performance_monitor.start_agent_execution(agent_name, task_name)
        
        try:
            self.logger.logger.info(f"Starting execution: {agent_name} - {task_name}")
            
            # Execute the task
            result = agent.execute_task(task)
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            # Extract output
            output = str(result) if result else ""
            output_length = len(output)
            
            # Get cost information from the current session
            cost_summary = self.bedrock_client.get_conversation_cost_summary()
            task_cost = cost_summary.get("total_cost", 0.0)
            
            # Token usage estimation
            token_usage = {
                "input_tokens": self.bedrock_client.token_counter.count_tokens(task.description),
                "output_tokens": self.bedrock_client.token_counter.count_tokens(output),
                "total_tokens": 0
            }
            token_usage["total_tokens"] = token_usage["input_tokens"] + token_usage["output_tokens"]
            
            # Complete agent execution tracking
            self.logger.performance_monitor.complete_agent_execution(
                start_timestamp,
                output_length,
                token_usage,
                task_cost,
                success=True
            )
            
            agent_result = AgentResult(
                agent_name=agent_name,
                task_name=task_name,
                output=output,
                execution_time_ms=duration_ms,
                token_usage=token_usage,
                cost=task_cost,
                success=True,
                metadata={
                    "output_length": output_length,
                    "start_time": start_timestamp
                }
            )
            
            self.logger.logger.info(
                f"Completed {agent_name} - {task_name} in {duration_ms:.2f}ms, "
                f"Output length: {output_length}, Cost: ${task_cost:.4f}"
            )
            
            return agent_result
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            error_message = str(e)
            
            # Complete agent execution tracking with error
            self.logger.performance_monitor.complete_agent_execution(
                start_timestamp,
                0,
                {},
                0.0,
                success=False,
                error_message=error_message
            )
            
            agent_result = AgentResult(
                agent_name=agent_name,
                task_name=task_name,
                output="",
                execution_time_ms=duration_ms,
                token_usage={},
                cost=0.0,
                success=False,
                error_message=error_message
            )
            
            self.logger.logger.error(
                f"Failed {agent_name} - {task_name} after {duration_ms:.2f}ms: {error_message}"
            )
            
            return agent_result


class EnhancedFinancialCrew:
    """Enhanced financial analysis crew with complete execution tracking"""
    
    def __init__(
        self,
        ticker: str,
        bedrock_client: EnhancedBedrockClient,
        tools: Dict[str, List[Any]]
    ):
        self.ticker = ticker
        self.bedrock_client = bedrock_client
        self.tools = tools
        self.logger = get_logger()
        
        # Initialize agents helper
        self.agents_helper = EnhancedFinancialAgents(bedrock_client, ticker)
        
        self.logger.logger.info(f"Enhanced Financial Crew initialized for {ticker}")
    
    @get_logger().log_function_call
    def execute_complete_analysis(self) -> CrewExecutionResult:
        """Execute complete financial analysis with all agents"""
        start_time = time.time()
        start_timestamp = datetime.now().isoformat()
        
        self.logger.logger.info(f"Starting complete financial analysis for {self.ticker}")
        
        agent_results = []
        total_cost = 0.0
        total_tokens = 0
        
        try:
            # 1. Technical Analysis
            technical_agent = self.agents_helper.create_agent("technical_analyst", self.tools.get("technical", []))
            technical_task = self.agents_helper.create_task("technical_analysis", technical_agent)
            technical_result = self.agents_helper.execute_agent_task(
                technical_agent, technical_task, "Technical Analyst", "Technical Analysis"
            )
            agent_results.append(technical_result)
            
            # 2. Fundamental Analysis
            fundamental_agent = self.agents_helper.create_agent("fundamental_analyst", self.tools.get("fundamental", []))
            fundamental_task = self.agents_helper.create_task("fundamental_analysis", fundamental_agent)
            fundamental_result = self.agents_helper.execute_agent_task(
                fundamental_agent, fundamental_task, "Fundamental Analyst", "Fundamental Analysis"
            )
            agent_results.append(fundamental_result)
            
            # 3. News Analysis
            news_agent = self.agents_helper.create_agent("news_analyst", self.tools.get("news", []))
            news_task = self.agents_helper.create_task("news_analysis", news_agent)
            news_result = self.agents_helper.execute_agent_task(
                news_agent, news_task, "News Analyst", "News Analysis"
            )
            agent_results.append(news_result)
            
            # 4. Investment Recommendation (synthesizes all previous analysis)
            advisor_agent = self.agents_helper.create_agent("investment_advisor", [])
            
            # Create context from previous analyses
            context_text = f"""
            TECHNICAL ANALYSIS RESULTS:
            {technical_result.output}
            
            FUNDAMENTAL ANALYSIS RESULTS:
            {fundamental_result.output}
            
            NEWS ANALYSIS RESULTS:
            {news_result.output}
            """
            
            advisor_task = self.agents_helper.create_task("investment_recommendation", advisor_agent)
            # Add context to the task description
            advisor_task.description += f"\n\nBASE YOUR RECOMMENDATION ON THE FOLLOWING ANALYSIS:\n{context_text}"
            
            advisor_result = self.agents_helper.execute_agent_task(
                advisor_agent, advisor_task, "Investment Advisor", "Investment Recommendation"
            )
            agent_results.append(advisor_result)
            
            # Calculate totals
            for result in agent_results:
                total_cost += result.cost
                total_tokens += result.token_usage.get("total_tokens", 0)
            
            end_time = time.time()
            total_duration_ms = (end_time - start_time) * 1000
            
            # Create final synthesis
            final_output = self._create_final_synthesis(agent_results)
            
            execution_result = CrewExecutionResult(
                crew_name="Enhanced Financial Analysis Crew",
                ticker=self.ticker,
                start_time=start_timestamp,
                end_time=datetime.now().isoformat(),
                total_duration_ms=total_duration_ms,
                agent_results=agent_results,
                final_output=final_output,
                total_cost=total_cost,
                total_tokens=total_tokens,
                success=True
            )
            
            self.logger.logger.info(
                f"Complete analysis finished for {self.ticker} in {total_duration_ms:.2f}ms, "
                f"Total cost: ${total_cost:.4f}, Total tokens: {total_tokens}"
            )
            
            return execution_result
            
        except Exception as e:
            end_time = time.time()
            total_duration_ms = (end_time - start_time) * 1000
            error_message = str(e)
            
            execution_result = CrewExecutionResult(
                crew_name="Enhanced Financial Analysis Crew",
                ticker=self.ticker,
                start_time=start_timestamp,
                end_time=datetime.now().isoformat(),
                total_duration_ms=total_duration_ms,
                agent_results=agent_results,
                final_output="",
                total_cost=total_cost,
                total_tokens=total_tokens,
                success=False,
                error_message=error_message
            )
            
            self.logger.logger.error(
                f"Complete analysis failed for {self.ticker} after {total_duration_ms:.2f}ms: {error_message}"
            )
            
            return execution_result
    
    def _create_final_synthesis(self, agent_results: List[AgentResult]) -> str:
        """Create a comprehensive synthesis of all agent results"""
        synthesis = f"""
# COMPREHENSIVE FINANCIAL ANALYSIS REPORT FOR {self.ticker}
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## EXECUTIVE SUMMARY
This report synthesizes analysis from multiple specialized financial agents to provide a comprehensive investment perspective.

"""
        
        for result in agent_results:
            if result.success:
                synthesis += f"""
## {result.agent_name.upper()} - {result.task_name.upper()}
Execution Time: {result.execution_time_ms:.2f}ms | Cost: ${result.cost:.4f} | Tokens: {result.token_usage.get('total_tokens', 0)}

{result.output}

---
"""
            else:
                synthesis += f"""
## {result.agent_name.upper()} - {result.task_name.upper()} (FAILED)
Error: {result.error_message}

---
"""
        
        # Add performance summary
        total_time = sum(r.execution_time_ms for r in agent_results)
        total_cost = sum(r.cost for r in agent_results)
        total_tokens = sum(r.token_usage.get('total_tokens', 0) for r in agent_results)
        successful_agents = sum(1 for r in agent_results if r.success)
        
        synthesis += f"""
## ANALYSIS PERFORMANCE SUMMARY
- Total Execution Time: {total_time:.2f}ms
- Total Cost: ${total_cost:.4f}
- Total Tokens: {total_tokens:,}
- Successful Agents: {successful_agents}/{len(agent_results)}
- Analysis Completion Rate: {(successful_agents/len(agent_results)*100):.1f}%
"""
        
        return synthesis
