"""
Test script for the FinMAS Chat Application

This script tests the core functionality of the chat interface without requiring
a full Streamlit environment.
"""

import os
import sys
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test core modules
        import streamlit
        print("✅ Streamlit imported successfully")
        
        import boto3
        print("✅ boto3 imported successfully")
        
        import crewai
        print("✅ CrewAI imported successfully")
        
        import yfinance
        print("✅ yfinance imported successfully")
        
        # Test our custom modules
        from bedrock_converse import BedrockConverseClient
        print("✅ BedrockConverseClient imported successfully")
        
        from chat_agents import ChatFinancialAgents
        print("✅ ChatFinancialAgents imported successfully")
        
        from chat_function_schemas import FUNCTION_SCHEMAS, get_all_function_names
        print("✅ Function schemas imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during imports: {e}")
        return False


def test_function_schemas():
    """Test function schemas are properly defined."""
    print("\nTesting function schemas...")
    
    try:
        from chat_function_schemas import FUNCTION_SCHEMAS, get_all_function_names, validate_function_call
        
        # Test schema structure
        if not FUNCTION_SCHEMAS:
            print("❌ No function schemas defined")
            return False
        
        print(f"✅ Found {len(FUNCTION_SCHEMAS)} function schemas")
        
        # Test function names
        function_names = get_all_function_names()
        expected_functions = [
            "analyze_stock",
            "get_technical_analysis", 
            "get_fundamental_analysis",
            "get_news_sentiment",
            "analyze_sec_filings",
            "compare_stocks",
            "get_market_overview",
            "get_investment_recommendation"
        ]
        
        for func in expected_functions:
            if func in function_names:
                print(f"✅ Function {func} found")
            else:
                print(f"❌ Function {func} missing")
                return False
        
        # Test validation
        valid_call = validate_function_call("analyze_stock", {"ticker": "AAPL", "analysis_types": ["technical"]})
        if valid_call:
            print("✅ Function validation works")
        else:
            print("❌ Function validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing function schemas: {e}")
        return False


def test_bedrock_client_creation():
    """Test Bedrock client creation (without actual AWS calls)."""
    print("\nTesting Bedrock client creation...")
    
    try:
        from bedrock_converse import BedrockConverseClient
        
        # Test client creation with dummy credentials
        client = BedrockConverseClient(
            model_id="anthropic.claude-3-5-sonnet-20241022-v2:0",
            region_name="us-east-1",
            aws_access_key_id="dummy",
            aws_secret_access_key="dummy"
        )
        
        print("✅ BedrockConverseClient created successfully")
        
        # Test basic methods
        client.add_message("user", "test message")
        history = client.get_conversation_history()
        
        if len(history) == 1:
            print("✅ Message handling works")
        else:
            print("❌ Message handling failed")
            return False
        
        # Test conversation clearing
        client.clear_conversation()
        history = client.get_conversation_history()
        
        if len(history) == 0:
            print("✅ Conversation clearing works")
        else:
            print("❌ Conversation clearing failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Bedrock client: {e}")
        return False


def test_chat_agents():
    """Test chat agents creation."""
    print("\nTesting chat agents...")
    
    try:
        from chat_agents import ChatFinancialAgents, StockDataTool, TechnicalAnalysisTool, NewsAnalysisTool
        
        # Test tool creation
        stock_tool = StockDataTool()
        print("✅ StockDataTool created successfully")
        
        tech_tool = TechnicalAnalysisTool()
        print("✅ TechnicalAnalysisTool created successfully")
        
        news_tool = NewsAnalysisTool()
        print("✅ NewsAnalysisTool created successfully")
        
        # Test tool execution (with a real ticker)
        try:
            result = stock_tool._run("AAPL", "1mo")
            if "AAPL" in result and "$" in result:
                print("✅ Stock data tool execution works")
            else:
                print("⚠️  Stock data tool returned unexpected format")
        except Exception as e:
            print(f"⚠️  Stock data tool execution failed (expected if no internet): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat agents: {e}")
        return False


def test_aws_credentials():
    """Test AWS credentials detection."""
    print("\nTesting AWS credentials detection...")
    
    # Test environment variables
    env_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY']
    env_configured = all(os.getenv(var) for var in env_vars)
    
    if env_configured:
        print("✅ AWS credentials found in environment variables")
    else:
        print("⚠️  No AWS credentials in environment variables")
    
    # Test boto3 default credential chain
    try:
        import boto3
        session = boto3.Session()
        credentials = session.get_credentials()
        
        if credentials:
            print("✅ AWS credentials found via boto3 default chain")
            return True
        else:
            print("⚠️  No AWS credentials found via boto3 default chain")
    except Exception as e:
        print(f"⚠️  Error checking boto3 credentials: {e}")
    
    return env_configured


def test_streamlit_app_structure():
    """Test that the Streamlit app has the correct structure."""
    print("\nTesting Streamlit app structure...")
    
    try:
        # Check if chat_app.py exists and has required functions
        app_file = Path(__file__).parent / "chat_app.py"
        
        if not app_file.exists():
            print("❌ chat_app.py not found")
            return False
        
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            "initialize_session_state",
            "check_aws_credentials", 
            "create_sidebar",
            "initialize_clients",
            "display_chat_messages",
            "process_user_message",
            "execute_function",
            "main"
        ]
        
        for func in required_functions:
            if f"def {func}" in content:
                print(f"✅ Function {func} found in chat_app.py")
            else:
                print(f"❌ Function {func} missing from chat_app.py")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Streamlit app structure: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing FinMAS Chat Application")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Function Schemas", test_function_schemas),
        ("Bedrock Client", test_bedrock_client_creation),
        ("Chat Agents", test_chat_agents),
        ("AWS Credentials", test_aws_credentials),
        ("Streamlit App Structure", test_streamlit_app_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The chat app should work correctly.")
        print("\n🚀 To run the chat app:")
        print("   streamlit run chat_app.py")
    elif passed >= 4:
        print("⚠️  Most tests passed. The app should work with some limitations.")
        print("\n🚀 To run the chat app:")
        print("   streamlit run chat_app.py")
    else:
        print("❌ Multiple tests failed. Please fix the issues above.")
    
    # Usage instructions
    print("\n" + "=" * 50)
    print("📖 Usage Instructions:")
    print("1. Ensure AWS credentials are configured")
    print("2. Run: streamlit run chat_app.py")
    print("3. Configure AWS credentials in the sidebar if needed")
    print("4. Start chatting with natural language queries like:")
    print("   - 'Analyze AAPL technical indicators'")
    print("   - 'Compare AAPL and MSFT fundamentals'")
    print("   - 'What's the news sentiment for TSLA?'")
    print("   - 'Should I invest in NVDA for long-term?'")


if __name__ == "__main__":
    main()
