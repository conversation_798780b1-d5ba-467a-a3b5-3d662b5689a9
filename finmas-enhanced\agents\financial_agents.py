"""
Enhanced Financial Analysis Agents

This module defines specialized financial analysis agents with comprehensive
capabilities and detailed output requirements.
"""

from typing import Dict, List, Any, Optional
from crewai import Agent, Task
from abc import ABC, abstractmethod

from ..utils.logging_utils import get_logger


class BaseFinancialAgent(ABC):
    """Base class for financial analysis agents"""
    
    def __init__(self, ticker: str, llm, tools: List[Any] = None):
        self.ticker = ticker
        self.llm = llm
        self.tools = tools or []
        self.logger = get_logger()
    
    @abstractmethod
    def get_agent_config(self) -> Dict[str, Any]:
        """Get agent configuration"""
        pass
    
    @abstractmethod
    def get_task_config(self) -> Dict[str, Any]:
        """Get task configuration"""
        pass
    
    def create_agent(self) -> Agent:
        """Create the CrewAI agent"""
        config = self.get_agent_config()
        return Agent(
            role=config["role"],
            goal=config["goal"],
            backstory=config["backstory"],
            verbose=config.get("verbose", True),
            allow_delegation=config.get("allow_delegation", False),
            llm=self.llm,
            tools=self.tools,
            memory=True
        )
    
    def create_task(self, agent: Agent, context: Optional[List[Task]] = None) -> Task:
        """Create the analysis task"""
        config = self.get_task_config()
        return Task(
            description=config["description"],
            expected_output=config["expected_output"],
            agent=agent,
            context=context or []
        )


class TechnicalAnalyst(BaseFinancialAgent):
    """Technical Analysis Agent"""
    
    def get_agent_config(self) -> Dict[str, Any]:
        return {
            "role": "Senior Technical Analyst",
            "goal": f"Conduct comprehensive technical analysis of {self.ticker} to identify trends, patterns, and trading opportunities",
            "backstory": """You are a highly experienced technical analyst with 15+ years in financial markets. 
            You specialize in chart pattern recognition, technical indicators, volume analysis, and price action trading. 
            You have a proven track record of identifying key support/resistance levels and trend reversals. 
            Your analysis is data-driven, precise, and actionable.""",
            "verbose": True,
            "allow_delegation": False
        }
    
    def get_task_config(self) -> Dict[str, Any]:
        return {
            "description": f"""
            Perform comprehensive technical analysis for {self.ticker}:
            
            1. **Price Trend Analysis**:
               - Analyze current price trends (short, medium, long-term)
               - Identify trend strength and momentum
               - Assess trend continuation vs reversal probability
            
            2. **Technical Indicators**:
               - Calculate and interpret RSI, MACD, Stochastic
               - Analyze moving averages (20, 50, 200-day)
               - Evaluate Bollinger Bands and volatility indicators
               - Include specific numerical values and signals
            
            3. **Support and Resistance**:
               - Identify key support and resistance levels
               - Analyze volume at these levels
               - Provide specific price targets
            
            4. **Chart Patterns**:
               - Identify any chart patterns (triangles, flags, head & shoulders, etc.)
               - Assess pattern completion and price implications
            
            5. **Volume Analysis**:
               - Analyze volume trends and patterns
               - Identify volume confirmation or divergence
            
            6. **Trading Recommendations**:
               - Provide specific entry and exit points
               - Set stop-loss and take-profit levels
               - Include risk/reward ratios
               - Specify time horizons for trades
            
            Include specific price levels, percentages, and quantitative metrics throughout your analysis.
            """,
            "expected_output": """Comprehensive technical analysis report with:
            - Current trend assessment with specific price levels
            - Technical indicator values and interpretations
            - Key support/resistance levels with price targets
            - Chart pattern analysis with implications
            - Volume analysis and confirmation signals
            - Specific trading recommendations with entry/exit points
            - Risk management guidelines with stop-loss levels
            - Short-term (1-4 weeks) and medium-term (1-3 months) outlook"""
        }


class FundamentalAnalyst(BaseFinancialAgent):
    """Fundamental Analysis Agent"""
    
    def get_agent_config(self) -> Dict[str, Any]:
        return {
            "role": "Senior Fundamental Analyst",
            "goal": f"Conduct in-depth fundamental analysis of {self.ticker} to assess financial health, valuation, and long-term investment potential",
            "backstory": """You are a seasoned fundamental analyst with 20+ years of experience in equity research. 
            You have deep expertise in financial statement analysis, valuation modeling, industry research, and competitive analysis. 
            You've covered multiple sectors and have a strong track record of identifying undervalued companies and growth opportunities. 
            Your analysis is thorough, quantitative, and focused on long-term value creation.""",
            "verbose": True,
            "allow_delegation": False
        }
    
    def get_task_config(self) -> Dict[str, Any]:
        return {
            "description": f"""
            Perform comprehensive fundamental analysis for {self.ticker}:
            
            1. **Financial Health Assessment**:
               - Analyze revenue growth trends (3-5 year history)
               - Evaluate profitability metrics (gross, operating, net margins)
               - Assess cash flow generation and quality
               - Review debt levels and capital structure
               - Include specific financial ratios and percentages
            
            2. **Valuation Analysis**:
               - Calculate P/E, P/B, P/S, EV/EBITDA ratios
               - Compare valuation metrics to industry peers
               - Perform DCF analysis or comparable company analysis
               - Determine fair value estimate with price target
            
            3. **Business Model Evaluation**:
               - Analyze revenue streams and business segments
               - Assess competitive advantages and moats
               - Evaluate market position and market share
               - Review management quality and strategy
            
            4. **Growth Prospects**:
               - Analyze historical and projected growth rates
               - Identify growth drivers and catalysts
               - Assess market expansion opportunities
               - Evaluate R&D investments and innovation pipeline
            
            5. **Risk Assessment**:
               - Identify key business and financial risks
               - Assess regulatory and competitive threats
               - Evaluate cyclical and secular headwinds
               - Review ESG factors and sustainability
            
            6. **Investment Recommendation**:
               - Provide clear buy/hold/sell recommendation
               - Set price target with upside/downside scenarios
               - Specify investment time horizon
               - Include position sizing recommendations
            
            Provide specific revenue numbers, growth rates, margins, and financial metrics throughout.
            """,
            "expected_output": """Detailed fundamental analysis report including:
            - Financial health assessment with specific metrics and ratios
            - Revenue and earnings growth analysis with historical data
            - Comprehensive valuation analysis with peer comparisons
            - Business model evaluation and competitive positioning
            - Growth prospects with specific drivers and catalysts
            - Risk assessment with mitigation strategies
            - Clear investment recommendation with price target
            - Quantitative metrics supporting all conclusions
            - Long-term (1-3 year) investment thesis"""
        }


class NewsAnalyst(BaseFinancialAgent):
    """News and Sentiment Analysis Agent"""
    
    def get_agent_config(self) -> Dict[str, Any]:
        return {
            "role": "Financial News and Sentiment Analyst",
            "goal": f"Analyze recent news, market sentiment, and catalysts for {self.ticker} to assess short-term impact and market perception",
            "backstory": """You are an expert financial news analyst with 12+ years of experience in market intelligence. 
            You specialize in interpreting earnings reports, corporate announcements, regulatory filings, and market sentiment. 
            You have a keen ability to identify market-moving catalysts and assess their impact on stock performance. 
            Your analysis helps investors understand the narrative driving stock movements.""",
            "verbose": True,
            "allow_delegation": False
        }
    
    def get_task_config(self) -> Dict[str, Any]:
        return {
            "description": f"""
            Conduct comprehensive news and sentiment analysis for {self.ticker}:
            
            1. **Recent News Analysis**:
               - Review latest news articles and press releases (last 30 days)
               - Analyze earnings reports and guidance updates
               - Assess corporate announcements and strategic initiatives
               - Evaluate analyst upgrades/downgrades and price target changes
            
            2. **Market Sentiment Assessment**:
               - Gauge overall market sentiment toward the stock
               - Analyze social media sentiment and retail investor interest
               - Review institutional investor positioning and flows
               - Assess options market sentiment (put/call ratios)
            
            3. **Catalyst Identification**:
               - Identify upcoming catalysts (earnings, product launches, etc.)
               - Assess potential impact of regulatory developments
               - Evaluate industry trends and their implications
               - Review merger & acquisition rumors or activities
            
            4. **Risk Factor Analysis**:
               - Identify negative news and potential headwinds
               - Assess litigation risks and regulatory concerns
               - Evaluate competitive threats and market disruption
               - Review ESG controversies and reputational risks
            
            5. **Market Impact Assessment**:
               - Quantify the impact of recent news on stock performance
               - Compare stock reaction to peer group and market
               - Assess whether news is already priced into the stock
               - Evaluate potential for continued momentum or reversal
            
            6. **Short-term Outlook**:
               - Provide near-term (1-3 months) sentiment outlook
               - Identify key events and dates to watch
               - Assess probability of positive vs negative surprises
            
            Include specific examples, dates, and quantitative impact assessments.
            """,
            "expected_output": """Comprehensive news and sentiment analysis including:
            - Recent news summary with impact assessment
            - Market sentiment analysis with supporting evidence
            - Catalyst identification with timeline and probability
            - Risk factor analysis with potential impact quantification
            - Market reaction analysis with performance comparisons
            - Short-term outlook with key events and dates
            - Sentiment score and recommendation for timing
            - Specific examples and quantitative metrics throughout"""
        }


class InvestmentAdvisor(BaseFinancialAgent):
    """Investment Advisory Agent"""
    
    def get_agent_config(self) -> Dict[str, Any]:
        return {
            "role": "Senior Investment Advisor",
            "goal": f"Synthesize all analysis to provide comprehensive investment recommendation for {self.ticker} with clear action plan",
            "backstory": """You are a senior investment advisor with 25+ years of experience managing portfolios for high-net-worth clients. 
            You excel at synthesizing complex financial analysis into clear, actionable investment recommendations. 
            You have a proven track record of generating alpha through disciplined investment processes and risk management. 
            Your recommendations are practical, well-reasoned, and tailored to different investor profiles.""",
            "verbose": True,
            "allow_delegation": False
        }
    
    def get_task_config(self) -> Dict[str, Any]:
        return {
            "description": f"""
            Synthesize all analysis to create comprehensive investment recommendation for {self.ticker}:
            
            1. **Analysis Integration**:
               - Synthesize technical, fundamental, and news analysis
               - Identify areas of agreement and disagreement between analyses
               - Weight different factors based on current market conditions
               - Resolve any conflicting signals or recommendations
            
            2. **Investment Rating**:
               - Provide clear investment rating (Strong Buy/Buy/Hold/Sell/Strong Sell)
               - Justify rating with specific supporting evidence
               - Include confidence level in the recommendation
               - Compare to analyst consensus and explain any differences
            
            3. **Price Target and Timeline**:
               - Set specific price target with upside/downside scenarios
               - Provide 6-month and 12-month price targets
               - Include probability-weighted expected returns
               - Specify key milestones and catalysts for target achievement
            
            4. **Risk Assessment**:
               - Identify and quantify key investment risks
               - Provide risk mitigation strategies
               - Assess risk-adjusted return potential
               - Include maximum drawdown expectations
            
            5. **Portfolio Recommendations**:
               - Suggest appropriate position sizing (% of portfolio)
               - Recommend investment approach (lump sum vs DCA)
               - Specify suitable investor profiles (growth, value, income)
               - Include diversification considerations
            
            6. **Action Plan**:
               - Provide specific entry and exit strategies
               - Set stop-loss and take-profit levels
               - Include rebalancing triggers and guidelines
               - Specify monitoring requirements and review schedule
            
            7. **Alternative Scenarios**:
               - Provide bull, base, and bear case scenarios
               - Include probability estimates for each scenario
               - Adjust recommendations based on scenario outcomes
            
            Base your synthesis on the provided technical, fundamental, and news analysis results.
            """,
            "expected_output": """Comprehensive investment recommendation including:
            - Clear investment rating with detailed justification
            - Specific price targets with timeline and scenarios
            - Risk assessment with quantified potential losses
            - Portfolio allocation recommendations with position sizing
            - Detailed action plan with entry/exit strategies
            - Alternative scenario analysis with probabilities
            - Monitoring guidelines and review schedule
            - Specific recommendations for different investor types
            - Quantitative metrics supporting all recommendations
            - Executive summary with key takeaways and next steps"""
        }
