#!/usr/bin/env python3
"""
Test Script for FinMAS Enhanced

This script validates the core functionality of the enhanced system
without requiring the full FinMAS installation.
"""

import sys
import os
from pathlib import Path
import time
from typing import Dict, Any

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """Test that all core modules can be imported"""
    print("Testing imports...")
    
    try:
        from utils.logging_utils import get_logger, setup_logging, CostTracker, PerformanceMonitor
        print("✅ Logging utilities imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import logging utilities: {e}")
        return False
    
    try:
        from utils.bedrock_utils import EnhancedBedrockClient, TokenCounter
        print("✅ Bedrock utilities imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Bedrock utilities: {e}")
        return False
    
    try:
        from utils.analytics import CostAnalytics, PerformanceAnalytics, ReportGenerator
        print("✅ Analytics utilities imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import analytics utilities: {e}")
        return False
    
    try:
        from agents.financial_agents import TechnicalAnalyst, FundamentalAnalyst, NewsAnalyst, InvestmentAdvisor
        print("✅ Financial agents imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import financial agents: {e}")
        return False
    
    return True


def test_logging_system():
    """Test the logging system functionality"""
    print("\nTesting logging system...")
    
    try:
        from utils.logging_utils import setup_logging
        
        # Initialize logger
        logger = setup_logging()
        print("✅ Logger initialized successfully")
        
        # Test cost tracker
        cost_tracker = logger.cost_tracker
        
        # Simulate an API call
        api_call = cost_tracker.log_api_call(
            model_id="us.anthropic.claude-3-5-sonnet-20241022-v2:0",
            input_tokens=100,
            output_tokens=200,
            function_name="test_function",
            duration_ms=1500.0,
            success=True
        )
        
        print(f"✅ API call logged: ${api_call.total_cost:.4f}")
        
        # Test session summary
        summary = cost_tracker.get_session_summary()
        print(f"✅ Session summary generated: {summary['total_calls']} calls, ${summary['total_cost']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Logging system test failed: {e}")
        return False


def test_token_counter():
    """Test the token counting functionality"""
    print("\nTesting token counter...")
    
    try:
        from utils.bedrock_utils import TokenCounter
        
        counter = TokenCounter()
        
        test_text = "This is a test message for token counting."
        token_count = counter.count_tokens(test_text)
        
        print(f"✅ Token counting works: '{test_text}' = {token_count} tokens")
        
        # Test message counting
        messages = [
            {"role": "user", "content": [{"text": "Hello, how are you?"}]},
            {"role": "assistant", "content": [{"text": "I'm doing well, thank you!"}]}
        ]
        
        total_tokens = counter.count_tokens_in_messages(messages)
        print(f"✅ Message token counting: {len(messages)} messages = {total_tokens} tokens")
        
        return True
        
    except Exception as e:
        print(f"❌ Token counter test failed: {e}")
        return False


def test_cost_analytics():
    """Test the cost analytics functionality"""
    print("\nTesting cost analytics...")
    
    try:
        from utils.analytics import CostAnalytics
        from utils.logging_utils import get_logger
        
        # Get logger and add some test data
        logger = get_logger()
        
        # Add multiple API calls for testing
        for i in range(5):
            logger.cost_tracker.log_api_call(
                model_id="us.anthropic.claude-3-5-sonnet-20241022-v2:0",
                input_tokens=100 + i * 10,
                output_tokens=200 + i * 20,
                function_name=f"test_function_{i}",
                duration_ms=1000.0 + i * 100,
                success=True
            )
        
        analytics = CostAnalytics()
        
        # Test cost trends
        trends = analytics.get_cost_trends(hours=1)
        if "error" not in trends:
            print(f"✅ Cost trends analysis: {trends['statistics']['total_calls']} calls analyzed")
        else:
            print(f"ℹ️ Cost trends: {trends['error']}")
        
        # Test cost prediction
        prediction = analytics.predict_session_cost(remaining_operations=3)
        if "error" not in prediction:
            print(f"✅ Cost prediction: ${prediction['predicted_additional_cost']:.4f} for 3 operations")
        else:
            print(f"ℹ️ Cost prediction: {prediction['error']}")
        
        # Test cost alerts
        alerts = analytics.get_cost_alerts(budget_limit=1.0)
        print(f"✅ Cost alerts generated: {len(alerts)} alerts")
        
        return True
        
    except Exception as e:
        print(f"❌ Cost analytics test failed: {e}")
        return False


def test_agent_configurations():
    """Test that agent configurations are properly defined"""
    print("\nTesting agent configurations...")
    
    try:
        from agents.financial_agents import TechnicalAnalyst, FundamentalAnalyst, NewsAnalyst, InvestmentAdvisor
        
        # Test agent creation (without LLM)
        ticker = "AAPL"
        
        # Technical Analyst
        tech_agent = TechnicalAnalyst(ticker, llm=None)
        tech_config = tech_agent.get_agent_config()
        tech_task_config = tech_agent.get_task_config()
        
        print(f"✅ Technical Analyst configured: {tech_config['role']}")
        print(f"   Task description length: {len(tech_task_config['description'])} chars")
        
        # Fundamental Analyst
        fund_agent = FundamentalAnalyst(ticker, llm=None)
        fund_config = fund_agent.get_agent_config()
        fund_task_config = fund_agent.get_task_config()
        
        print(f"✅ Fundamental Analyst configured: {fund_config['role']}")
        print(f"   Task description length: {len(fund_task_config['description'])} chars")
        
        # News Analyst
        news_agent = NewsAnalyst(ticker, llm=None)
        news_config = news_agent.get_agent_config()
        news_task_config = news_agent.get_task_config()
        
        print(f"✅ News Analyst configured: {news_config['role']}")
        print(f"   Task description length: {len(news_task_config['description'])} chars")
        
        # Investment Advisor
        advisor_agent = InvestmentAdvisor(ticker, llm=None)
        advisor_config = advisor_agent.get_agent_config()
        advisor_task_config = advisor_agent.get_task_config()
        
        print(f"✅ Investment Advisor configured: {advisor_config['role']}")
        print(f"   Task description length: {len(advisor_task_config['description'])} chars")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent configuration test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import (
            BEDROCK_CONFIG, MODEL_PRICING, ANALYSIS_CONFIG,
            AVAILABLE_MODELS, DEFAULT_ANALYSIS_PARAMS, get_env_config
        )
        
        print(f"✅ Bedrock config loaded: {BEDROCK_CONFIG['default_model']}")
        print(f"✅ Model pricing loaded: {len(MODEL_PRICING)} models")
        print(f"✅ Available models: {len(AVAILABLE_MODELS)} models")
        
        env_config = get_env_config()
        print(f"✅ Environment config loaded: region={env_config['aws_region']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def run_all_tests():
    """Run all validation tests"""
    print("🚀 Starting FinMAS Enhanced System Validation\n")
    
    tests = [
        ("Import Tests", test_imports),
        ("Logging System", test_logging_system),
        ("Token Counter", test_token_counter),
        ("Cost Analytics", test_cost_analytics),
        ("Agent Configurations", test_agent_configurations),
        ("Configuration", test_configuration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! FinMAS Enhanced is ready to use.")
        return True
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
