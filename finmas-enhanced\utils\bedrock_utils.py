"""
Enhanced AWS Bedrock Utilities with Comprehensive Cost Tracking

This module provides enhanced Bedrock integration with:
- Detailed cost tracking and token counting
- Function calling support with the Converse API
- Comprehensive error handling and retry logic
- Performance monitoring and logging
"""

import boto3
import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
import tiktoken
from botocore.exceptions import ClientError, BotoCoreError

from .logging_utils import get_logger


class TokenCounter:
    """Utility class for counting tokens in text"""
    
    def __init__(self, model_name: str = "cl100k_base"):
        """Initialize token counter with specified encoding"""
        try:
            self.encoding = tiktoken.get_encoding(model_name)
        except Exception:
            # Fallback to a basic estimation if tiktok<PERSON> fails
            self.encoding = None
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        if self.encoding:
            return len(self.encoding.encode(text))
        else:
            # Rough estimation: ~4 characters per token
            return len(text) // 4
    
    def count_tokens_in_messages(self, messages: List[Dict[str, Any]]) -> int:
        """Count tokens in a list of messages"""
        total_tokens = 0
        for message in messages:
            if isinstance(message.get('content'), list):
                for content_block in message['content']:
                    if 'text' in content_block:
                        total_tokens += self.count_tokens(content_block['text'])
            elif isinstance(message.get('content'), str):
                total_tokens += self.count_tokens(message['content'])
        return total_tokens


class EnhancedBedrockClient:
    """Enhanced Bedrock client with comprehensive logging and cost tracking"""
    
    def __init__(
        self,
        model_id: str = "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        region_name: str = "us-east-1",
        aws_access_key_id: Optional[str] = None,
        aws_secret_access_key: Optional[str] = None,
        aws_session_token: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        """Initialize enhanced Bedrock client"""
        self.model_id = model_id
        self.region_name = region_name
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Initialize logger
        self.logger = get_logger()
        
        # Initialize token counter
        self.token_counter = TokenCounter()
        
        # Initialize boto3 client
        session_kwargs = {"region_name": region_name}
        if aws_access_key_id and aws_secret_access_key:
            session_kwargs.update({
                "aws_access_key_id": aws_access_key_id,
                "aws_secret_access_key": aws_secret_access_key
            })
            if aws_session_token:
                session_kwargs["aws_session_token"] = aws_session_token
        
        session = boto3.Session(**session_kwargs)
        self.client = session.client('bedrock-runtime')
        
        # Conversation history
        self.conversation_history: List[Dict[str, Any]] = []
        
        self.logger.logger.info(f"Enhanced Bedrock client initialized with model: {model_id}")
    
    @get_logger().log_function_call
    def converse_with_cost_tracking(
        self,
        messages: List[Dict[str, Any]],
        system_message: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.1,
        tools: Optional[List[Dict[str, Any]]] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Call Bedrock Converse API with comprehensive cost tracking
        
        Returns:
            Tuple of (response, cost_info)
        """
        start_time = time.time()
        function_name = f"{self.__class__.__name__}.converse_with_cost_tracking"
        
        # Count input tokens
        input_tokens = self.token_counter.count_tokens_in_messages(messages)
        if system_message:
            input_tokens += self.token_counter.count_tokens(system_message)
        
        # Prepare request
        request_body = {
            "modelId": self.model_id,
            "messages": messages,
            "inferenceConfig": {
                "maxTokens": max_tokens,
                "temperature": temperature
            }
        }
        
        if system_message:
            request_body["system"] = [{"text": system_message}]
        
        if tools:
            request_body["toolConfig"] = {"tools": tools}
        
        # Execute with retry logic
        response = None
        error_message = None
        
        for attempt in range(self.max_retries):
            try:
                response = self.client.converse(**request_body)
                break
            except (ClientError, BotoCoreError) as e:
                error_message = str(e)
                self.logger.logger.warning(
                    f"Bedrock API call attempt {attempt + 1} failed: {error_message}"
                )
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    raise
        
        duration_ms = (time.time() - start_time) * 1000
        
        if response:
            # Extract usage information
            usage = response.get('usage', {})
            output_tokens = usage.get('outputTokens', 0)
            actual_input_tokens = usage.get('inputTokens', input_tokens)
            
            # Log API call with cost tracking
            api_call = self.logger.cost_tracker.log_api_call(
                model_id=self.model_id,
                input_tokens=actual_input_tokens,
                output_tokens=output_tokens,
                function_name=function_name,
                duration_ms=duration_ms,
                success=True
            )
            
            cost_info = {
                "input_tokens": actual_input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": actual_input_tokens + output_tokens,
                "input_cost": api_call.input_cost,
                "output_cost": api_call.output_cost,
                "total_cost": api_call.total_cost,
                "duration_ms": duration_ms
            }
            
            self.logger.logger.info(
                f"Bedrock API call successful - Tokens: {actual_input_tokens}+{output_tokens}={actual_input_tokens + output_tokens}, "
                f"Cost: ${api_call.total_cost:.4f}, Duration: {duration_ms:.2f}ms"
            )
            
            return response, cost_info
        
        else:
            # Log failed API call
            self.logger.cost_tracker.log_api_call(
                model_id=self.model_id,
                input_tokens=input_tokens,
                output_tokens=0,
                function_name=function_name,
                duration_ms=duration_ms,
                success=False,
                error_message=error_message
            )
            
            raise Exception(f"Bedrock API call failed after {self.max_retries} attempts: {error_message}")
    
    @get_logger().log_function_call
    def simple_completion(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Simple text completion with cost tracking
        
        Returns:
            Tuple of (response_text, cost_info)
        """
        messages = [{"role": "user", "content": [{"text": prompt}]}]
        
        response, cost_info = self.converse_with_cost_tracking(
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        # Extract text response
        response_text = ""
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        for content_block in content:
            if 'text' in content_block:
                response_text += content_block['text']
        
        return response_text, cost_info
    
    @get_logger().log_function_call
    def function_calling_completion(
        self,
        messages: List[Dict[str, Any]],
        tools: List[Dict[str, Any]],
        system_message: Optional[str] = None,
        max_tokens: int = 2000,
        temperature: float = 0.1
    ) -> Tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """
        Function calling completion with cost tracking
        
        Returns:
            Tuple of (response_text, function_calls, cost_info)
        """
        response, cost_info = self.converse_with_cost_tracking(
            messages=messages,
            system_message=system_message,
            max_tokens=max_tokens,
            temperature=temperature,
            tools=tools
        )
        
        # Extract response content
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        response_text = ""
        function_calls = []
        
        for content_block in content:
            if 'text' in content_block:
                response_text += content_block['text']
            elif 'toolUse' in content_block:
                tool_use = content_block['toolUse']
                function_calls.append({
                    'toolUseId': tool_use['toolUseId'],
                    'name': tool_use['name'],
                    'input': tool_use['input']
                })
        
        return response_text, function_calls, cost_info
    
    def add_message_to_history(self, role: str, content: Union[str, List[Dict[str, Any]]]):
        """Add a message to conversation history"""
        if isinstance(content, str):
            content = [{"text": content}]
        
        message = {
            "role": role,
            "content": content
        }
        self.conversation_history.append(message)
    
    def get_conversation_cost_summary(self) -> Dict[str, Any]:
        """Get cost summary for current conversation"""
        return self.logger.cost_tracker.get_session_summary()
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history = []
        self.logger.logger.info("Conversation history cleared")
    
    def export_conversation(self) -> Dict[str, Any]:
        """Export conversation with cost information"""
        return {
            "model_id": self.model_id,
            "timestamp": datetime.now().isoformat(),
            "conversation": self.conversation_history,
            "cost_summary": self.get_conversation_cost_summary()
        }
