"""
AWS Bedrock Converse API Integration

This module provides integration with AWS Bedrock's Converse API for function calling
and conversational interactions with financial analysis capabilities.
"""

import boto3
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from chat_function_schemas import FUNCTION_SCHEMAS, validate_function_call


logger = logging.getLogger(__name__)


class BedrockConverseClient:
    """Client for AWS Bedrock Converse API with function calling support."""
    
    def __init__(
        self,
        model_id: str = "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        region_name: str = "us-east-1",
        aws_access_key_id: Optional[str] = None,
        aws_secret_access_key: Optional[str] = None,
        aws_session_token: Optional[str] = None
    ):
        """Initialize the Bedrock Converse client."""
        self.model_id = model_id
        self.region_name = region_name
        
        # Initialize boto3 client
        session_kwargs = {"region_name": region_name}
        if aws_access_key_id and aws_secret_access_key:
            session_kwargs.update({
                "aws_access_key_id": aws_access_key_id,
                "aws_secret_access_key": aws_secret_access_key
            })
            if aws_session_token:
                session_kwargs["aws_session_token"] = aws_session_token
        
        session = boto3.Session(**session_kwargs)
        self.client = session.client('bedrock-runtime')
        
        # Conversation history
        self.conversation_history: List[Dict[str, Any]] = []
        
        # System message for financial analysis
        self.system_message = """You are FinMAS, a sophisticated financial analysis assistant powered by AWS Bedrock. You have access to comprehensive financial analysis tools including:

- Technical analysis with price trends and indicators
- Fundamental analysis with financial metrics and ratios  
- News sentiment analysis and recent developments
- SEC filing analysis for regulatory insights
- Multi-stock comparisons and market overviews
- Investment recommendations based on risk tolerance

When users ask about stocks or financial analysis, use the available functions to gather data and provide comprehensive insights. Always explain your analysis clearly and provide actionable recommendations when appropriate.

Key capabilities:
- Real-time stock data analysis
- Technical indicator calculations (SMA, RSI, MACD, etc.)
- Financial health assessments
- News sentiment tracking
- SEC filing insights
- Investment recommendations

Be conversational, helpful, and thorough in your analysis while maintaining professional financial advisory standards."""

    def add_message(self, role: str, content: str) -> None:
        """Add a message to the conversation history."""
        message = {
            "role": role,
            "content": [{"text": content}]
        }
        self.conversation_history.append(message)

    def converse_with_functions(
        self,
        user_message: str,
        max_tokens: int = 2000,
        temperature: float = 0.1
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Have a conversation with function calling support.
        
        Returns:
            Tuple of (response_text, function_calls)
        """
        # Add user message to history
        self.add_message("user", user_message)
        
        try:
            # Prepare the converse request
            request_body = {
                "modelId": self.model_id,
                "messages": self.conversation_history,
                "system": [{"text": self.system_message}],
                "inferenceConfig": {
                    "maxTokens": max_tokens,
                    "temperature": temperature
                },
                "toolConfig": {
                    "tools": FUNCTION_SCHEMAS
                }
            }
            
            # Call the Converse API
            response = self.client.converse(**request_body)
            
            # Extract the response
            output_message = response['output']['message']
            response_content = output_message.get('content', [])
            
            # Process the response
            text_response = ""
            function_calls = []
            
            for content_block in response_content:
                if 'text' in content_block:
                    text_response += content_block['text']
                elif 'toolUse' in content_block:
                    tool_use = content_block['toolUse']
                    function_calls.append({
                        'toolUseId': tool_use['toolUseId'],
                        'name': tool_use['name'],
                        'input': tool_use['input']
                    })
            
            # Add assistant response to history
            self.conversation_history.append(output_message)
            
            return text_response, function_calls
            
        except Exception as e:
            logger.error(f"Error in converse_with_functions: {str(e)}")
            return f"I apologize, but I encountered an error: {str(e)}", []

    def process_function_calls(
        self,
        function_calls: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Process function calls and return results.
        
        This method should be implemented to actually execute the functions
        and return their results.
        """
        results = []
        
        for func_call in function_calls:
            function_name = func_call['name']
            parameters = func_call['input']
            tool_use_id = func_call['toolUseId']
            
            # Validate the function call
            if not validate_function_call(function_name, parameters):
                result = {
                    'toolUseId': tool_use_id,
                    'content': [{"text": f"Invalid parameters for function {function_name}"}],
                    'status': 'error'
                }
            else:
                # Execute the function (placeholder - will be implemented in the main app)
                result = {
                    'toolUseId': tool_use_id,
                    'content': [{"text": f"Function {function_name} called with parameters: {json.dumps(parameters, indent=2)}"}],
                    'status': 'success'
                }
            
            results.append(result)
        
        return results

    def continue_conversation_with_function_results(
        self,
        function_results: List[Dict[str, Any]],
        max_tokens: int = 2000,
        temperature: float = 0.1
    ) -> str:
        """
        Continue the conversation after function execution.
        
        Args:
            function_results: Results from function execution
            max_tokens: Maximum tokens for response
            temperature: Temperature for response generation
            
        Returns:
            Assistant's response text
        """
        try:
            # Add function results to conversation history
            for result in function_results:
                tool_result_message = {
                    "role": "user",
                    "content": [
                        {
                            "toolResult": {
                                "toolUseId": result['toolUseId'],
                                "content": result['content']
                            }
                        }
                    ]
                }
                self.conversation_history.append(tool_result_message)
            
            # Continue the conversation
            request_body = {
                "modelId": self.model_id,
                "messages": self.conversation_history,
                "system": [{"text": self.system_message}],
                "inferenceConfig": {
                    "maxTokens": max_tokens,
                    "temperature": temperature
                },
                "toolConfig": {
                    "tools": FUNCTION_SCHEMAS
                }
            }
            
            response = self.client.converse(**request_body)
            
            # Extract response text
            output_message = response['output']['message']
            response_content = output_message.get('content', [])
            
            text_response = ""
            for content_block in response_content:
                if 'text' in content_block:
                    text_response += content_block['text']
            
            # Add assistant response to history
            self.conversation_history.append(output_message)
            
            return text_response
            
        except Exception as e:
            logger.error(f"Error in continue_conversation_with_function_results: {str(e)}")
            return f"I apologize, but I encountered an error processing the results: {str(e)}"

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the current conversation history."""
        return self.conversation_history.copy()

    def clear_conversation(self) -> None:
        """Clear the conversation history."""
        self.conversation_history = []

    def export_conversation(self) -> str:
        """Export conversation history as JSON string."""
        export_data = {
            "model_id": self.model_id,
            "timestamp": datetime.now().isoformat(),
            "conversation": self.conversation_history
        }
        return json.dumps(export_data, indent=2)

    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation."""
        if not self.conversation_history:
            return "No conversation history available."
        
        user_messages = [
            msg for msg in self.conversation_history 
            if msg.get('role') == 'user'
        ]
        
        assistant_messages = [
            msg for msg in self.conversation_history 
            if msg.get('role') == 'assistant'
        ]
        
        return f"Conversation contains {len(user_messages)} user messages and {len(assistant_messages)} assistant responses."
