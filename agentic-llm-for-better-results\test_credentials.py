"""
Test AWS credentials detection for the Streamlit app.
"""

import os

def test_env_vars():
    """Test environment variable detection."""
    print("Testing environment variable detection...")
    
    vars_to_check = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY', 
        'AWS_DEFAULT_REGION'
    ]
    
    for var in vars_to_check:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: SET (length: {len(value)})")
        else:
            print(f"❌ {var}: NOT SET")
    
    # Check if basic credentials are available
    basic_creds = os.getenv('AWS_ACCESS_KEY_ID') and os.getenv('AWS_SECRET_ACCESS_KEY')
    print(f"\n🔐 Basic AWS credentials: {'✅ AVAILABLE' if basic_creds else '❌ MISSING'}")
    
    return basic_creds

def test_boto3_default():
    """Test if boto3 can find credentials using default methods."""
    print("\nTesting boto3 default credential detection...")
    
    try:
        import boto3
        
        # Try to create a session and get credentials
        session = boto3.Session()
        credentials = session.get_credentials()
        
        if credentials:
            print("✅ boto3 found credentials via default methods")
            print(f"   Access Key: {credentials.access_key[:8]}...")
            return True
        else:
            print("❌ boto3 could not find credentials")
            return False
            
    except Exception as e:
        print(f"❌ boto3 credential test failed: {e}")
        return False

def test_aws_cli():
    """Test if AWS CLI is configured."""
    print("\nTesting AWS CLI configuration...")
    
    try:
        import subprocess
        result = subprocess.run(['aws', 'configure', 'list'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ AWS CLI is configured")
            print("   Configuration:")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print("❌ AWS CLI not configured or not found")
            return False
            
    except FileNotFoundError:
        print("❌ AWS CLI not installed")
        return False
    except Exception as e:
        print(f"❌ AWS CLI test failed: {e}")
        return False

def main():
    """Run all credential tests."""
    print("🔐 AWS Credentials Detection Test")
    print("=" * 40)
    
    tests = [
        ("Environment Variables", test_env_vars),
        ("Boto3 Default Detection", test_boto3_default),
        ("AWS CLI Configuration", test_aws_cli),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Summary:")
    
    any_method_works = any(results.values())
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {'✅ CREDENTIALS AVAILABLE' if any_method_works else '❌ NO CREDENTIALS FOUND'}")
    
    if any_method_works:
        print("\n🚀 The Streamlit app should be able to access AWS Bedrock!")
    else:
        print("\n⚠️  No AWS credentials found. Options:")
        print("   1. Set environment variables: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY")
        print("   2. Configure AWS CLI: aws configure")
        print("   3. Use the manual credential entry in the Streamlit app")

if __name__ == "__main__":
    main()
