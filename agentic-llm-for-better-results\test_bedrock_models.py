"""
Test script to verify which Bedrock model IDs work with Converse API
"""

import boto3
import json
from typing import List, Dict, Any


def test_model_id(client, model_id: str) -> Dict[str, Any]:
    """Test a specific model ID with the Converse API."""
    try:
        response = client.converse(
            modelId=model_id,
            messages=[
                {
                    "role": "user",
                    "content": [{"text": "Hello, can you respond with just 'Working' if you receive this?"}]
                }
            ],
            inferenceConfig={
                "maxTokens": 50,
                "temperature": 0.1
            }
        )
        
        # Extract response text
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        response_text = ""
        for block in content:
            if 'text' in block:
                response_text += block['text']
        
        return {
            "model_id": model_id,
            "status": "success",
            "response": response_text.strip(),
            "usage": response.get('usage', {})
        }
        
    except Exception as e:
        return {
            "model_id": model_id,
            "status": "error",
            "error": str(e),
            "response": None
        }


def main():
    """Test various model IDs to find working ones."""
    print("🧪 Testing Bedrock Model IDs with Converse API")
    print("=" * 60)
    
    # Initialize Bedrock client
    try:
        client = boto3.client('bedrock-runtime', region_name='us-east-1')
        print("✅ Bedrock client initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Bedrock client: {e}")
        return
    
    # Model IDs to test
    model_ids_to_test = [
        # Inference Profile IDs (recommended)
        "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
        "us.anthropic.claude-3-5-sonnet-20240620-v1:0", 
        "us.anthropic.claude-3-5-haiku-20241022-v1:0",
        
        # Direct Model IDs (may not work with on-demand)
        "anthropic.claude-3-5-sonnet-20241022-v2:0",
        "anthropic.claude-3-5-sonnet-20240620-v1:0",
        "anthropic.claude-3-5-haiku-20241022-v1:0",
        "anthropic.claude-3-sonnet-20240229-v1:0",
        "anthropic.claude-3-haiku-20240307-v1:0",
        
        # Alternative inference profiles
        "us.anthropic.claude-3-sonnet-20240229-v1:0",
        "us.anthropic.claude-3-haiku-20240307-v1:0",
    ]
    
    print(f"\n🔍 Testing {len(model_ids_to_test)} model IDs...")
    
    working_models = []
    failed_models = []
    
    for i, model_id in enumerate(model_ids_to_test, 1):
        print(f"\n[{i}/{len(model_ids_to_test)}] Testing: {model_id}")
        
        result = test_model_id(client, model_id)
        
        if result["status"] == "success":
            print(f"✅ SUCCESS - Response: {result['response']}")
            working_models.append(result)
        else:
            print(f"❌ FAILED - Error: {result['error']}")
            failed_models.append(result)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    print(f"\n✅ Working Models ({len(working_models)}):")
    if working_models:
        for model in working_models:
            print(f"  • {model['model_id']}")
            if 'usage' in model and model['usage']:
                usage = model['usage']
                print(f"    Input tokens: {usage.get('inputTokens', 'N/A')}")
                print(f"    Output tokens: {usage.get('outputTokens', 'N/A')}")
    else:
        print("  None found")
    
    print(f"\n❌ Failed Models ({len(failed_models)}):")
    if failed_models:
        for model in failed_models:
            print(f"  • {model['model_id']}")
            print(f"    Error: {model['error']}")
    else:
        print("  None")
    
    # Recommendations
    print("\n" + "=" * 60)
    print("💡 Recommendations")
    print("=" * 60)
    
    if working_models:
        best_model = working_models[0]
        print(f"\n🎯 Recommended Model ID: {best_model['model_id']}")
        print("\n📝 Update your configuration:")
        print(f"   Default model_id = \"{best_model['model_id']}\"")
        
        # Generate updated model list for the app
        print(f"\n🔧 Updated model list for chat_app.py:")
        print("available_models = {")
        for model in working_models:
            model_name = model['model_id'].replace('us.anthropic.', '').replace('anthropic.', '')
            model_name = model_name.replace('-', ' ').title()
            if 'us.anthropic' in model['model_id']:
                model_name += " (Inference Profile)"
            print(f"    \"{model['model_id']}\": \"{model_name}\",")
        print("}")
        
    else:
        print("\n⚠️  No working models found. Please check:")
        print("   1. AWS credentials are configured correctly")
        print("   2. You have access to Bedrock in the us-east-1 region")
        print("   3. Claude models are enabled in your AWS account")
        print("   4. Your AWS account has the necessary permissions")
    
    print(f"\n🏁 Testing complete!")


if __name__ == "__main__":
    main()
