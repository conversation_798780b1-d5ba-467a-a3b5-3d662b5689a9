"""
Advanced Analytics and Reporting for FinMAS Enhanced

This module provides comprehensive analytics capabilities including:
- Real-time cost monitoring and alerts
- Performance benchmarking and optimization insights
- Detailed execution reports and summaries
- Cost prediction and budgeting tools
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
from dataclasses import asdict

from .logging_utils import get_logger, BedrockAPICall, AgentExecution, FunctionCall


class CostAnalytics:
    """Advanced cost analytics and monitoring"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def get_cost_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Analyze cost trends over specified time period"""
        api_calls = self.logger.cost_tracker.api_calls
        
        if not api_calls:
            return {"error": "No API calls to analyze"}
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame([asdict(call) for call in api_calls])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Filter by time period
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_calls = df[df['timestamp'] >= cutoff_time]
        
        if recent_calls.empty:
            return {"error": f"No API calls in the last {hours} hours"}
        
        # Calculate trends
        hourly_costs = recent_calls.groupby(
            recent_calls['timestamp'].dt.floor('H')
        )['total_cost'].sum()
        
        # Cost statistics
        stats = {
            "total_cost": recent_calls['total_cost'].sum(),
            "average_cost_per_call": recent_calls['total_cost'].mean(),
            "median_cost_per_call": recent_calls['total_cost'].median(),
            "max_cost_call": recent_calls['total_cost'].max(),
            "min_cost_call": recent_calls['total_cost'].min(),
            "total_calls": len(recent_calls),
            "total_tokens": recent_calls['total_tokens'].sum(),
            "average_tokens_per_call": recent_calls['total_tokens'].mean(),
            "cost_per_1k_tokens": (recent_calls['total_cost'].sum() / recent_calls['total_tokens'].sum()) * 1000 if recent_calls['total_tokens'].sum() > 0 else 0
        }
        
        # Model breakdown
        model_stats = recent_calls.groupby('model_id').agg({
            'total_cost': ['sum', 'count', 'mean'],
            'total_tokens': 'sum',
            'duration_ms': 'mean'
        }).round(4)
        
        # Hourly trend
        hourly_trend = hourly_costs.to_dict()
        
        return {
            "period_hours": hours,
            "statistics": stats,
            "model_breakdown": model_stats.to_dict(),
            "hourly_trend": hourly_trend,
            "cost_efficiency": self._calculate_cost_efficiency(recent_calls)
        }
    
    def _calculate_cost_efficiency(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate cost efficiency metrics"""
        if df.empty:
            return {}
        
        return {
            "tokens_per_dollar": df['total_tokens'].sum() / df['total_cost'].sum() if df['total_cost'].sum() > 0 else 0,
            "average_response_time": df['duration_ms'].mean(),
            "cost_per_second": df['total_cost'].sum() / (df['duration_ms'].sum() / 1000) if df['duration_ms'].sum() > 0 else 0,
            "success_rate": df['success'].mean() * 100
        }
    
    def predict_session_cost(self, remaining_operations: int) -> Dict[str, float]:
        """Predict cost for remaining operations in session"""
        api_calls = self.logger.cost_tracker.api_calls
        
        if not api_calls:
            return {"error": "No historical data for prediction"}
        
        # Calculate average cost per operation
        recent_calls = api_calls[-10:]  # Use last 10 calls for prediction
        avg_cost = sum(call.total_cost for call in recent_calls) / len(recent_calls)
        
        predicted_cost = avg_cost * remaining_operations
        current_cost = self.logger.cost_tracker.total_cost
        
        return {
            "current_session_cost": current_cost,
            "predicted_additional_cost": predicted_cost,
            "predicted_total_cost": current_cost + predicted_cost,
            "average_cost_per_operation": avg_cost,
            "confidence": min(len(recent_calls) / 10.0, 1.0)  # Confidence based on sample size
        }
    
    def get_cost_alerts(self, budget_limit: float = 10.0) -> List[Dict[str, Any]]:
        """Generate cost alerts and warnings"""
        alerts = []
        current_cost = self.logger.cost_tracker.total_cost
        
        # Budget alerts
        if current_cost > budget_limit:
            alerts.append({
                "type": "budget_exceeded",
                "severity": "high",
                "message": f"Session cost ${current_cost:.4f} exceeds budget limit ${budget_limit:.4f}",
                "recommendation": "Consider optimizing model selection or reducing token usage"
            })
        elif current_cost > budget_limit * 0.8:
            alerts.append({
                "type": "budget_warning",
                "severity": "medium",
                "message": f"Session cost ${current_cost:.4f} is approaching budget limit ${budget_limit:.4f}",
                "recommendation": "Monitor remaining operations carefully"
            })
        
        # Efficiency alerts
        api_calls = self.logger.cost_tracker.api_calls
        if len(api_calls) >= 5:
            recent_calls = api_calls[-5:]
            avg_cost = sum(call.total_cost for call in recent_calls) / len(recent_calls)
            
            if avg_cost > 0.1:  # High cost per call threshold
                alerts.append({
                    "type": "high_cost_per_call",
                    "severity": "medium",
                    "message": f"Recent calls averaging ${avg_cost:.4f} per call",
                    "recommendation": "Consider using a more cost-effective model or reducing max_tokens"
                })
        
        return alerts


class PerformanceAnalytics:
    """Performance analytics and optimization insights"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def get_agent_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive agent performance summary"""
        executions = self.logger.performance_monitor.agent_executions
        
        if not executions:
            return {"error": "No agent executions to analyze"}
        
        # Convert to DataFrame
        df = pd.DataFrame([asdict(execution) for execution in executions])
        
        # Overall statistics
        total_executions = len(df)
        successful_executions = df['status'].eq('completed').sum()
        success_rate = (successful_executions / total_executions) * 100
        
        # Performance by agent
        agent_stats = df.groupby('agent_name').agg({
            'duration_ms': ['mean', 'min', 'max', 'std'],
            'cost': ['sum', 'mean'],
            'output_length': 'mean',
            'status': lambda x: (x == 'completed').mean() * 100
        }).round(2)
        
        # Performance trends
        df['start_time'] = pd.to_datetime(df['start_time'])
        df_sorted = df.sort_values('start_time')
        
        return {
            "total_executions": total_executions,
            "success_rate": success_rate,
            "average_execution_time": df['duration_ms'].mean(),
            "total_cost": df['cost'].sum(),
            "agent_performance": agent_stats.to_dict(),
            "performance_trends": self._calculate_performance_trends(df_sorted)
        }
    
    def _calculate_performance_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate performance trends over time"""
        if len(df) < 2:
            return {"insufficient_data": True}
        
        # Calculate rolling averages
        df['execution_time_ma'] = df['duration_ms'].rolling(window=3, min_periods=1).mean()
        df['cost_ma'] = df['cost'].rolling(window=3, min_periods=1).mean()
        
        # Trend analysis
        recent_avg_time = df['execution_time_ma'].tail(3).mean()
        early_avg_time = df['execution_time_ma'].head(3).mean()
        
        time_trend = "improving" if recent_avg_time < early_avg_time else "degrading"
        
        return {
            "execution_time_trend": time_trend,
            "time_improvement_pct": ((early_avg_time - recent_avg_time) / early_avg_time) * 100 if early_avg_time > 0 else 0,
            "recent_average_time": recent_avg_time,
            "early_average_time": early_avg_time
        }
    
    def get_optimization_recommendations(self) -> List[Dict[str, str]]:
        """Generate optimization recommendations based on performance data"""
        recommendations = []
        
        # Analyze API calls for optimization opportunities
        api_calls = self.logger.cost_tracker.api_calls
        if api_calls:
            # High token usage recommendation
            avg_tokens = sum(call.total_tokens for call in api_calls) / len(api_calls)
            if avg_tokens > 3000:
                recommendations.append({
                    "category": "token_optimization",
                    "recommendation": "Consider reducing max_tokens parameter to optimize costs",
                    "impact": "Could reduce costs by 20-30%",
                    "priority": "medium"
                })
            
            # Model selection recommendation
            model_costs = {}
            for call in api_calls:
                if call.model_id not in model_costs:
                    model_costs[call.model_id] = []
                model_costs[call.model_id].append(call.total_cost)
            
            if len(model_costs) > 1:
                avg_costs = {model: sum(costs)/len(costs) for model, costs in model_costs.items()}
                cheapest_model = min(avg_costs, key=avg_costs.get)
                most_expensive = max(avg_costs, key=avg_costs.get)
                
                if avg_costs[most_expensive] > avg_costs[cheapest_model] * 1.5:
                    recommendations.append({
                        "category": "model_selection",
                        "recommendation": f"Consider using {cheapest_model} instead of {most_expensive} for cost savings",
                        "impact": f"Could reduce costs by {((avg_costs[most_expensive] - avg_costs[cheapest_model]) / avg_costs[most_expensive]) * 100:.1f}%",
                        "priority": "high"
                    })
        
        # Agent performance recommendations
        executions = self.logger.performance_monitor.agent_executions
        if executions:
            failed_executions = [e for e in executions if e.status == 'failed']
            if len(failed_executions) > 0:
                recommendations.append({
                    "category": "reliability",
                    "recommendation": "Implement retry logic for failed agent executions",
                    "impact": "Could improve success rate",
                    "priority": "high"
                })
        
        return recommendations


class ReportGenerator:
    """Generate comprehensive reports and summaries"""
    
    def __init__(self):
        self.logger = get_logger()
        self.cost_analytics = CostAnalytics()
        self.performance_analytics = PerformanceAnalytics()
    
    def generate_session_report(self) -> Dict[str, Any]:
        """Generate comprehensive session report"""
        session_data = self.logger.export_session_data()
        cost_trends = self.cost_analytics.get_cost_trends(hours=24)
        performance_summary = self.performance_analytics.get_agent_performance_summary()
        optimization_recs = self.performance_analytics.get_optimization_recommendations()
        
        return {
            "session_info": {
                "session_id": session_data["session_id"],
                "timestamp": session_data["timestamp"],
                "duration": self._calculate_session_duration(session_data)
            },
            "cost_analysis": cost_trends,
            "performance_analysis": performance_summary,
            "optimization_recommendations": optimization_recs,
            "raw_data": session_data
        }
    
    def _calculate_session_duration(self, session_data: Dict[str, Any]) -> str:
        """Calculate session duration"""
        if not session_data.get("function_calls"):
            return "Unknown"
        
        timestamps = [call["timestamp"] for call in session_data["function_calls"]]
        if len(timestamps) < 2:
            return "< 1 minute"
        
        start_time = min(timestamps)
        end_time = max(timestamps)
        
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        duration = end_dt - start_dt
        return str(duration).split('.')[0]  # Remove microseconds
    
    def export_report_to_json(self, filename: Optional[str] = None) -> str:
        """Export session report to JSON file"""
        report = self.generate_session_report()
        
        if filename is None:
            filename = f"finmas_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.logger.log_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return str(filepath)
