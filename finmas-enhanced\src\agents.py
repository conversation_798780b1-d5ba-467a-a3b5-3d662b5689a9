"""
Multi-Agent Financial Analysis System

This module implements specialized financial analysis agents that
collaborate to provide comprehensive market analysis.
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod

from .data_sources import FinancialDataProvider, StockData, FinancialMetrics, TechnicalIndicators, NewsItem
from .bedrock_client import BedrockClient
from config import Config
from utils.logging_utils import get_logger


@dataclass
class AgentResult:
    """Represents the result from an agent analysis"""
    agent_name: str
    ticker: str
    analysis: str
    confidence: float
    key_metrics: Dict[str, Any]
    recommendations: List[str]
    timestamp: str
    execution_time_ms: float
    cost: float


class BaseAgent(ABC):
    """Base class for financial analysis agents"""
    
    def __init__(self, name: str, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        self.name = name
        self.bedrock_client = bedrock_client
        self.data_provider = data_provider
        self.logger = get_logger()
    
    @abstractmethod
    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform analysis for the given ticker"""
        pass
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent"""
        pass


class TechnicalAnalyst(BaseAgent):
    """Technical analysis specialist agent"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        super().__init__("Technical Analyst", bedrock_client, data_provider)
    
    def get_system_prompt(self) -> str:
        return """You are a senior technical analyst with 15+ years of experience in financial markets. You specialize in:

- Chart pattern recognition and price action analysis
- Technical indicators (RSI, MACD, Moving Averages, Bollinger Bands)
- Support and resistance level identification
- Trend analysis and momentum assessment
- Volume analysis and market sentiment
- Risk management and position sizing

Your analysis should be:
- Data-driven and objective
- Include specific price levels and targets
- Provide clear entry/exit points
- Assess risk/reward ratios
- Consider multiple timeframes
- Include actionable trading recommendations

Always provide specific numerical values, price targets, and risk levels. Be precise and professional."""
    
    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform technical analysis"""
        import time
        start_time = time.time()
        
        try:
            self.logger.logger.info(f"Technical Analyst analyzing {ticker}")
            
            # Get technical data
            stock_data = await self.data_provider.get_stock_data(ticker)
            technical_indicators = await self.data_provider.get_technical_indicators(ticker)
            
            if not stock_data or not technical_indicators:
                raise ValueError(f"Unable to get technical data for {ticker}")
            
            # Prepare analysis prompt
            analysis_prompt = f"""Perform comprehensive technical analysis for {ticker}:

CURRENT PRICE DATA:
- Current Price: ${stock_data.current_price:.2f}
- Change: {stock_data.change:+.2f} ({stock_data.change_percent:+.2f}%)
- Volume: {stock_data.volume:,}
- 52-Week High: ${stock_data.fifty_two_week_high:.2f}
- 52-Week Low: ${stock_data.fifty_two_week_low:.2f}

TECHNICAL INDICATORS:
- RSI (14): {technical_indicators.rsi:.1f}
- MACD: {technical_indicators.macd}
- Moving Averages: {technical_indicators.moving_averages}
- Bollinger Bands: {technical_indicators.bollinger_bands}
- Support Levels: {technical_indicators.support_levels}
- Resistance Levels: {technical_indicators.resistance_levels}
- Current Trend: {technical_indicators.trend}
- Momentum: {technical_indicators.momentum}

Provide a comprehensive technical analysis including:
1. Current trend assessment and strength
2. Key support and resistance levels
3. Technical indicator analysis and signals
4. Entry and exit recommendations
5. Risk management guidelines
6. Price targets (short and medium term)
7. Overall technical rating and confidence level

Be specific with price levels and provide actionable insights."""
            
            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]
            
            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=2000,
                temperature=0.1
            )
            
            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Extract key metrics
            key_metrics = {
                "current_price": stock_data.current_price,
                "rsi": technical_indicators.rsi,
                "trend": technical_indicators.trend,
                "momentum": technical_indicators.momentum,
                "support_levels": technical_indicators.support_levels,
                "resistance_levels": technical_indicators.resistance_levels
            }
            
            # Generate recommendations
            recommendations = self._extract_recommendations(response_text, technical_indicators)
            
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.85,  # High confidence for technical analysis
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=stock_data.timestamp,
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )
            
        except Exception as e:
            self.logger.logger.error(f"Technical analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Technical analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp="",
                execution_time_ms=execution_time,
                cost=0.0
            )
    
    def _extract_recommendations(self, analysis: str, indicators: TechnicalIndicators) -> List[str]:
        """Extract actionable recommendations from analysis"""
        recommendations = []
        
        # Basic recommendation logic based on indicators
        if indicators.rsi and indicators.rsi > 70:
            recommendations.append("Consider taking profits - RSI indicates overbought conditions")
        elif indicators.rsi and indicators.rsi < 30:
            recommendations.append("Potential buying opportunity - RSI indicates oversold conditions")
        
        if indicators.trend == "bullish":
            recommendations.append("Trend is bullish - consider long positions")
        elif indicators.trend == "bearish":
            recommendations.append("Trend is bearish - consider defensive positioning")
        
        if not recommendations:
            recommendations.append("Monitor price action for clearer signals")
        
        return recommendations


class FundamentalAnalyst(BaseAgent):
    """Fundamental analysis specialist agent"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        super().__init__("Fundamental Analyst", bedrock_client, data_provider)
    
    def get_system_prompt(self) -> str:
        return """You are a senior fundamental analyst with 20+ years of experience in equity research. You specialize in:

- Financial statement analysis and ratio interpretation
- Valuation modeling (DCF, comparable company analysis)
- Industry and competitive analysis
- Business model evaluation
- Growth prospects assessment
- Risk factor identification

Your analysis should be:
- Thorough and data-driven
- Include specific financial metrics and ratios
- Provide valuation estimates and price targets
- Assess competitive positioning
- Identify key risks and opportunities
- Include investment recommendations with time horizons

Always provide specific numbers, ratios, and quantitative assessments. Be analytical and precise."""
    
    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform fundamental analysis"""
        import time
        start_time = time.time()
        
        try:
            self.logger.logger.info(f"Fundamental Analyst analyzing {ticker}")
            
            # Get fundamental data
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)
            
            if not stock_data or not financial_metrics:
                raise ValueError(f"Unable to get fundamental data for {ticker}")
            
            # Prepare analysis prompt
            analysis_prompt = f"""Perform comprehensive fundamental analysis for {ticker}:

VALUATION METRICS:
- Current Price: ${stock_data.current_price:.2f}
- Market Cap: ${financial_metrics.revenue_ttm/1e9:.1f}B (if available)
- P/E Ratio: {stock_data.pe_ratio:.1f if stock_data.pe_ratio else 'N/A'}
- Dividend Yield: {stock_data.dividend_yield*100:.2f}% (if available)

FINANCIAL PERFORMANCE:
- Revenue (TTM): ${financial_metrics.revenue_ttm/1e9:.1f}B (if available)
- Revenue Growth: {financial_metrics.revenue_growth*100:.1f}% (if available)
- Net Income: ${financial_metrics.net_income/1e9:.1f}B (if available)
- Gross Margin: {financial_metrics.gross_margin*100:.1f}% (if available)
- Operating Margin: {financial_metrics.operating_margin*100:.1f}% (if available)
- Profit Margin: {financial_metrics.profit_margin*100:.1f}% (if available)

FINANCIAL HEALTH:
- ROE: {financial_metrics.roe*100:.1f}% (if available)
- ROA: {financial_metrics.roa*100:.1f}% (if available)
- Debt-to-Equity: {financial_metrics.debt_to_equity:.2f} (if available)
- Current Ratio: {financial_metrics.current_ratio:.2f} (if available)
- Free Cash Flow: ${financial_metrics.free_cash_flow/1e9:.1f}B (if available)
- EPS: ${financial_metrics.earnings_per_share:.2f} (if available)

Provide comprehensive fundamental analysis including:
1. Financial health assessment
2. Valuation analysis and fair value estimate
3. Growth prospects and drivers
4. Competitive positioning
5. Key risks and opportunities
6. Investment recommendation with price target
7. Time horizon and confidence level

Be specific with numbers and provide clear investment thesis."""
            
            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]
            
            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=2000,
                temperature=0.1
            )
            
            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Extract key metrics
            key_metrics = {
                "pe_ratio": stock_data.pe_ratio,
                "revenue_growth": financial_metrics.revenue_growth,
                "profit_margin": financial_metrics.profit_margin,
                "roe": financial_metrics.roe,
                "debt_to_equity": financial_metrics.debt_to_equity,
                "current_ratio": financial_metrics.current_ratio
            }
            
            # Generate recommendations
            recommendations = self._extract_recommendations(financial_metrics)
            
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.80,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=financial_metrics.timestamp,
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )
            
        except Exception as e:
            self.logger.logger.error(f"Fundamental analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000
            
            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Fundamental analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp="",
                execution_time_ms=execution_time,
                cost=0.0
            )
    
    def _extract_recommendations(self, metrics: FinancialMetrics) -> List[str]:
        """Extract recommendations based on financial metrics"""
        recommendations = []
        
        if metrics.roe and metrics.roe > 0.15:
            recommendations.append("Strong return on equity indicates efficient management")
        
        if metrics.debt_to_equity and metrics.debt_to_equity < 0.5:
            recommendations.append("Conservative debt levels provide financial stability")
        
        if metrics.revenue_growth and metrics.revenue_growth > 0.1:
            recommendations.append("Strong revenue growth indicates business expansion")
        
        if not recommendations:
            recommendations.append("Mixed fundamental signals - requires deeper analysis")
        
        return recommendations


class NewsAnalyst(BaseAgent):
    """News and sentiment analysis specialist agent"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        super().__init__("News Analyst", bedrock_client, data_provider)

    def get_system_prompt(self) -> str:
        return """You are a financial news analyst with expertise in market sentiment analysis. You specialize in:

- News impact assessment and catalyst identification
- Market sentiment analysis and interpretation
- Event-driven trading opportunities
- Risk factor identification from news flow
- Earnings and corporate announcement analysis
- Regulatory and industry development impact

Your analysis should be:
- Timely and relevant to current market conditions
- Assess both positive and negative sentiment drivers
- Identify potential catalysts and risk factors
- Provide sentiment scoring and market impact assessment
- Consider short-term and medium-term implications
- Include actionable insights for investors

Always provide specific examples from the news and quantify sentiment impact where possible."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform news and sentiment analysis"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"News Analyst analyzing {ticker}")

            # Get news data
            news_items = await self.data_provider.get_news(ticker)
            stock_data = await self.data_provider.get_stock_data(ticker)

            if not news_items:
                raise ValueError(f"Unable to get news data for {ticker}")

            # Prepare news summary
            news_summary = "\n".join([
                f"- {item.title} ({item.source}, {item.published_date})"
                for item in news_items[:10]
            ])

            # Prepare analysis prompt
            analysis_prompt = f"""Analyze recent news and market sentiment for {ticker}:

CURRENT STOCK INFO:
- Current Price: ${stock_data.current_price:.2f}
- Daily Change: {stock_data.change:+.2f} ({stock_data.change_percent:+.2f}%)
- Volume: {stock_data.volume:,}

RECENT NEWS HEADLINES:
{news_summary}

Provide comprehensive news and sentiment analysis including:
1. Overall sentiment assessment (bullish/bearish/neutral)
2. Key positive and negative catalysts identified
3. Market impact assessment of recent news
4. Potential upcoming catalysts or events
5. Risk factors emerging from news flow
6. Short-term sentiment outlook
7. Sentiment score (1-10 scale) with reasoning

Focus on actionable insights and specific impact on stock performance."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]

            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=1500,
                temperature=0.2
            )

            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])

            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']

            # Extract key metrics
            key_metrics = {
                "news_count": len(news_items),
                "recent_volume": stock_data.volume,
                "price_change": stock_data.change_percent
            }

            # Generate recommendations
            recommendations = self._extract_recommendations(news_items, stock_data)

            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.75,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=stock_data.timestamp,
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )

        except Exception as e:
            self.logger.logger.error(f"News analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"News analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp="",
                execution_time_ms=execution_time,
                cost=0.0
            )

    def _extract_recommendations(self, news_items: List[NewsItem], stock_data: StockData) -> List[str]:
        """Extract recommendations based on news sentiment"""
        recommendations = []

        if len(news_items) > 5:
            recommendations.append("High news flow - monitor for volatility")

        if stock_data.volume > 1000000:  # High volume threshold
            recommendations.append("Above-average volume suggests increased interest")

        if abs(stock_data.change_percent) > 5:
            recommendations.append("Significant price movement - check for news catalysts")

        if not recommendations:
            recommendations.append("Monitor news flow for emerging catalysts")

        return recommendations


class InvestmentAdvisor(BaseAgent):
    """Investment advisory specialist agent"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        super().__init__("Investment Advisor", bedrock_client, data_provider)

    def get_system_prompt(self) -> str:
        return """You are a senior investment advisor with 25+ years of experience managing portfolios. You specialize in:

- Synthesizing technical, fundamental, and sentiment analysis
- Investment recommendation formulation
- Risk assessment and portfolio allocation
- Time horizon and strategy alignment
- Market timing and entry/exit strategies
- Portfolio diversification and risk management

Your recommendations should be:
- Clear and actionable with specific buy/sell/hold ratings
- Include price targets and time horizons
- Assess risk-reward ratios
- Consider different investor profiles (conservative, moderate, aggressive)
- Provide position sizing guidance
- Include stop-loss and profit-taking levels
- Synthesize all available analysis into coherent investment thesis

Always provide specific recommendations with clear reasoning and risk assessment."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Provide investment recommendation based on all available analysis"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"Investment Advisor analyzing {ticker}")

            # Get basic stock data
            stock_data = await self.data_provider.get_stock_data(ticker)

            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")

            # Prepare synthesis prompt with context from other agents
            context_summary = ""
            if context:
                for agent_name, result in context.items():
                    if isinstance(result, AgentResult) and result.analysis:
                        context_summary += f"\n{agent_name.upper()} ANALYSIS:\n{result.analysis}\n"

            analysis_prompt = f"""Provide comprehensive investment recommendation for {ticker}:

CURRENT STOCK DATA:
- Current Price: ${stock_data.current_price:.2f}
- Change: {stock_data.change:+.2f} ({stock_data.change_percent:+.2f}%)
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)
- P/E Ratio: {stock_data.pe_ratio:.1f if stock_data.pe_ratio else 'N/A'}

ANALYSIS FROM SPECIALIST TEAMS:
{context_summary}

Based on all available analysis, provide:
1. Overall investment rating (Strong Buy/Buy/Hold/Sell/Strong Sell)
2. Price target with 6-month and 12-month horizons
3. Key investment thesis and supporting factors
4. Risk assessment and potential downside
5. Position sizing recommendations for different risk profiles
6. Entry strategy and optimal timing
7. Stop-loss and profit-taking levels
8. Portfolio allocation suggestions
9. Investment confidence level and reasoning

Synthesize all analysis into a coherent investment recommendation with specific actionable guidance."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]

            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=2000,
                temperature=0.1
            )

            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])

            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']

            # Extract key metrics
            key_metrics = {
                "current_price": stock_data.current_price,
                "market_cap": stock_data.market_cap,
                "pe_ratio": stock_data.pe_ratio
            }

            # Generate final recommendations
            recommendations = self._extract_recommendations(stock_data, context)

            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.90,  # High confidence as synthesis of all analysis
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=stock_data.timestamp,
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )

        except Exception as e:
            self.logger.logger.error(f"Investment advisory failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Investment advisory failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp="",
                execution_time_ms=execution_time,
                cost=0.0
            )

    def _extract_recommendations(self, stock_data: StockData, context: Optional[Dict[str, Any]]) -> List[str]:
        """Extract final investment recommendations"""
        recommendations = []

        # Basic recommendations based on available data
        if stock_data.pe_ratio and stock_data.pe_ratio < 15:
            recommendations.append("Attractive valuation - consider accumulation")
        elif stock_data.pe_ratio and stock_data.pe_ratio > 30:
            recommendations.append("High valuation - exercise caution")

        if context:
            # Count positive vs negative signals from other agents
            positive_signals = 0
            total_signals = 0

            for result in context.values():
                if isinstance(result, AgentResult):
                    total_signals += 1
                    if result.confidence > 0.7:
                        positive_signals += 1

            if total_signals > 0:
                signal_ratio = positive_signals / total_signals
                if signal_ratio > 0.7:
                    recommendations.append("Multiple positive signals - consider buying")
                elif signal_ratio < 0.3:
                    recommendations.append("Multiple negative signals - consider reducing exposure")

        if not recommendations:
            recommendations.append("Mixed signals - maintain current position and monitor")

        return recommendations


class MarketAnalyst(BaseAgent):
    """Market and sector analysis specialist agent"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        super().__init__("Market Analyst", bedrock_client, data_provider)

    def get_system_prompt(self) -> str:
        return """You are a senior market analyst with expertise in sector analysis and market dynamics. You specialize in:

- Sector and industry analysis
- Market trend identification and correlation analysis
- Economic indicator interpretation
- Peer comparison and relative performance
- Market cycle analysis and positioning
- Macro-economic impact assessment

Your analysis should be:
- Provide sector context and industry positioning
- Analyze market trends and correlations
- Compare performance against peers and benchmarks
- Assess macro-economic influences
- Identify sector rotation opportunities
- Consider market cycle implications

Always provide market context and relative performance analysis."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform market and sector analysis"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"Market Analyst analyzing {ticker}")

            # Get stock data
            stock_data = await self.data_provider.get_stock_data(ticker)

            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")

            # Prepare analysis prompt with market context
            analysis_prompt = f"""Perform comprehensive market and sector analysis for {ticker}:

STOCK PERFORMANCE:
- Current Price: ${stock_data.current_price:.2f}
- Daily Change: {stock_data.change:+.2f} ({stock_data.change_percent:+.2f}%)
- Volume: {stock_data.volume:,}
- Market Cap: ${stock_data.market_cap/1e9:.1f}B (if available)
- Beta: {stock_data.beta:.2f if stock_data.beta else 'N/A'}

Provide comprehensive market analysis including:
1. Sector classification and industry positioning
2. Market trend analysis and correlation with major indices
3. Peer comparison and relative performance
4. Economic environment impact assessment
5. Sector rotation implications
6. Market cycle positioning
7. Relative valuation vs sector/market
8. Market sentiment and institutional positioning

Focus on market context and relative performance analysis."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]

            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=1800,
                temperature=0.1
            )

            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])

            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']

            # Extract key metrics
            key_metrics = {
                "beta": stock_data.beta,
                "market_cap": stock_data.market_cap,
                "volume": stock_data.volume,
                "price_change": stock_data.change_percent
            }

            # Generate recommendations
            recommendations = self._extract_recommendations(stock_data)

            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.75,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=stock_data.timestamp,
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )

        except Exception as e:
            self.logger.logger.error(f"Market analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Market analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp="",
                execution_time_ms=execution_time,
                cost=0.0
            )

    def _extract_recommendations(self, stock_data: StockData) -> List[str]:
        """Extract market-based recommendations"""
        recommendations = []

        if stock_data.beta and stock_data.beta > 1.5:
            recommendations.append("High beta stock - expect amplified market movements")
        elif stock_data.beta and stock_data.beta < 0.5:
            recommendations.append("Low beta stock - defensive characteristics")

        if abs(stock_data.change_percent) > 3:
            recommendations.append("Significant price movement - check for sector-wide trends")

        if not recommendations:
            recommendations.append("Monitor sector trends and market correlation")

        return recommendations


class RiskAnalyst(BaseAgent):
    """Risk assessment and management specialist agent"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        super().__init__("Risk Analyst", bedrock_client, data_provider)

    def get_system_prompt(self) -> str:
        return """You are a senior risk analyst with expertise in financial risk assessment. You specialize in:

- Volatility analysis and risk metrics calculation
- Downside risk assessment and scenario analysis
- Liquidity risk and market impact analysis
- Credit risk and financial stability assessment
- Regulatory and compliance risk evaluation
- Portfolio risk and correlation analysis

Your analysis should be:
- Quantify risk metrics and volatility measures
- Assess downside scenarios and tail risks
- Evaluate liquidity and market risks
- Identify regulatory and operational risks
- Provide risk-adjusted return analysis
- Recommend risk management strategies

Always provide specific risk metrics and mitigation strategies."""

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Perform comprehensive risk analysis"""
        import time
        start_time = time.time()

        try:
            self.logger.logger.info(f"Risk Analyst analyzing {ticker}")

            # Get data for risk analysis
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)
            technical_indicators = await self.data_provider.get_technical_indicators(ticker)

            if not stock_data:
                raise ValueError(f"Unable to get stock data for {ticker}")

            # Prepare risk analysis prompt
            analysis_prompt = f"""Perform comprehensive risk analysis for {ticker}:

PRICE AND VOLATILITY DATA:
- Current Price: ${stock_data.current_price:.2f}
- 52-Week Range: ${stock_data.fifty_two_week_low:.2f} - ${stock_data.fifty_two_week_high:.2f}
- Beta: {stock_data.beta:.2f if stock_data.beta else 'N/A'}
- Daily Volume: {stock_data.volume:,}

FINANCIAL RISK METRICS:
- Debt-to-Equity: {financial_metrics.debt_to_equity:.2f if financial_metrics and financial_metrics.debt_to_equity else 'N/A'}
- Current Ratio: {financial_metrics.current_ratio:.2f if financial_metrics and financial_metrics.current_ratio else 'N/A'}
- Free Cash Flow: {f'${financial_metrics.free_cash_flow/1e9:.1f}B' if financial_metrics and financial_metrics.free_cash_flow else 'N/A'}

TECHNICAL RISK INDICATORS:
- RSI: {technical_indicators.rsi:.1f if technical_indicators and technical_indicators.rsi else 'N/A'}
- Trend: {technical_indicators.trend if technical_indicators else 'N/A'}
- Support Levels: {technical_indicators.support_levels if technical_indicators else 'N/A'}

Provide comprehensive risk analysis including:
1. Volatility assessment and risk metrics
2. Downside risk and maximum drawdown potential
3. Liquidity risk and trading volume analysis
4. Financial stability and credit risk assessment
5. Regulatory and industry-specific risks
6. Scenario analysis (bull/base/bear cases)
7. Risk-adjusted return expectations
8. Risk mitigation strategies and position sizing

Focus on quantifiable risk metrics and practical risk management."""

            # Get LLM analysis
            messages = [{"role": "user", "content": [{"text": analysis_prompt}]}]

            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.get_system_prompt(),
                max_tokens=2000,
                temperature=0.1
            )

            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])

            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']

            # Calculate risk metrics
            price_range = stock_data.fifty_two_week_high - stock_data.fifty_two_week_low
            volatility_estimate = (price_range / stock_data.current_price) * 100

            key_metrics = {
                "beta": stock_data.beta,
                "volatility_estimate": volatility_estimate,
                "debt_to_equity": financial_metrics.debt_to_equity if financial_metrics else None,
                "current_ratio": financial_metrics.current_ratio if financial_metrics else None,
                "rsi": technical_indicators.rsi if technical_indicators else None
            }

            # Generate risk-focused recommendations
            recommendations = self._extract_recommendations(stock_data, financial_metrics, technical_indicators)

            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=response_text,
                confidence=0.85,
                key_metrics=key_metrics,
                recommendations=recommendations,
                timestamp=stock_data.timestamp,
                execution_time_ms=execution_time,
                cost=cost_info['total_cost']
            )

        except Exception as e:
            self.logger.logger.error(f"Risk analysis failed for {ticker}: {str(e)}")
            execution_time = (time.time() - start_time) * 1000

            return AgentResult(
                agent_name=self.name,
                ticker=ticker,
                analysis=f"Risk analysis failed: {str(e)}",
                confidence=0.0,
                key_metrics={},
                recommendations=[],
                timestamp="",
                execution_time_ms=execution_time,
                cost=0.0
            )

    def _extract_recommendations(self, stock_data: StockData, financial_metrics: Optional[FinancialMetrics],
                               technical_indicators: Optional[TechnicalIndicators]) -> List[str]:
        """Extract risk-focused recommendations"""
        recommendations = []

        # Volatility-based recommendations
        if stock_data.beta and stock_data.beta > 1.5:
            recommendations.append("High volatility - consider smaller position sizes")

        # Financial health recommendations
        if financial_metrics and financial_metrics.debt_to_equity and financial_metrics.debt_to_equity > 1.0:
            recommendations.append("High debt levels - monitor financial stability")

        if financial_metrics and financial_metrics.current_ratio and financial_metrics.current_ratio < 1.0:
            recommendations.append("Liquidity concerns - assess short-term financial health")

        # Technical risk recommendations
        if technical_indicators and technical_indicators.rsi:
            if technical_indicators.rsi > 80:
                recommendations.append("Extremely overbought - high reversal risk")
            elif technical_indicators.rsi < 20:
                recommendations.append("Extremely oversold - potential bounce but verify fundamentals")

        if not recommendations:
            recommendations.append("Moderate risk profile - standard position sizing appropriate")

        return recommendations


class AgentCoordinator:
    """Coordinates multiple agents for collaborative analysis"""

    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        self.bedrock_client = bedrock_client
        self.data_provider = data_provider
        self.logger = get_logger()

        # Initialize all agents
        self.agents = {
            "technical": TechnicalAnalyst(bedrock_client, data_provider),
            "fundamental": FundamentalAnalyst(bedrock_client, data_provider),
            "news": NewsAnalyst(bedrock_client, data_provider),
            "market": MarketAnalyst(bedrock_client, data_provider),
            "risk": RiskAnalyst(bedrock_client, data_provider),
            "investment": InvestmentAdvisor(bedrock_client, data_provider)
        }

        self.logger.logger.info("Agent Coordinator initialized with 6 specialized agents")

    async def run_collaborative_analysis(
        self,
        ticker: str,
        agent_types: List[str] = None,
        collaboration_mode: str = "sequential"
    ) -> Dict[str, AgentResult]:
        """
        Run collaborative analysis with multiple agents

        Args:
            ticker: Stock ticker to analyze
            agent_types: List of agent types to run (default: all)
            collaboration_mode: "sequential", "parallel", or "hierarchical"

        Returns:
            Dictionary of agent results
        """
        if agent_types is None:
            agent_types = ["technical", "fundamental", "news", "market", "risk"]

        self.logger.logger.info(f"Starting collaborative analysis for {ticker} with agents: {agent_types}")

        if collaboration_mode == "parallel":
            return await self._run_parallel_analysis(ticker, agent_types)
        elif collaboration_mode == "hierarchical":
            return await self._run_hierarchical_analysis(ticker, agent_types)
        else:  # sequential
            return await self._run_sequential_analysis(ticker, agent_types)

    async def _run_parallel_analysis(self, ticker: str, agent_types: List[str]) -> Dict[str, AgentResult]:
        """Run agents in parallel for speed"""
        import asyncio

        tasks = []
        for agent_type in agent_types:
            if agent_type in self.agents:
                tasks.append(self.agents[agent_type].analyze(ticker))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        agent_results = {}
        for i, result in enumerate(results):
            agent_type = agent_types[i]
            if isinstance(result, AgentResult):
                agent_results[agent_type] = result
            else:
                self.logger.logger.error(f"Agent {agent_type} failed: {str(result)}")

        return agent_results

    async def _run_sequential_analysis(self, ticker: str, agent_types: List[str]) -> Dict[str, AgentResult]:
        """Run agents sequentially, passing context between them"""
        agent_results = {}
        context = {}

        for agent_type in agent_types:
            if agent_type in self.agents:
                try:
                    result = await self.agents[agent_type].analyze(ticker, context)
                    agent_results[agent_type] = result

                    # Add result to context for next agents
                    context[agent_type] = result

                except Exception as e:
                    self.logger.logger.error(f"Agent {agent_type} failed: {str(e)}")

        return agent_results

    async def _run_hierarchical_analysis(self, ticker: str, agent_types: List[str]) -> Dict[str, AgentResult]:
        """Run agents in hierarchical order with dependencies"""
        agent_results = {}

        # Phase 1: Data gathering agents (parallel)
        data_agents = ["technical", "fundamental", "news", "market"]
        phase1_agents = [agent for agent in data_agents if agent in agent_types]

        if phase1_agents:
            phase1_results = await self._run_parallel_analysis(ticker, phase1_agents)
            agent_results.update(phase1_results)

        # Phase 2: Analysis agents (sequential with context)
        analysis_agents = ["risk"]
        for agent_type in analysis_agents:
            if agent_type in agent_types and agent_type in self.agents:
                try:
                    result = await self.agents[agent_type].analyze(ticker, agent_results)
                    agent_results[agent_type] = result
                except Exception as e:
                    self.logger.logger.error(f"Agent {agent_type} failed: {str(e)}")

        # Phase 3: Synthesis agent (with all context)
        if "investment" in agent_types:
            try:
                result = await self.agents["investment"].analyze(ticker, agent_results)
                agent_results["investment"] = result
            except Exception as e:
                self.logger.logger.error(f"Investment advisor failed: {str(e)}")

        return agent_results

    async def synthesize_results(
        self,
        ticker: str,
        agent_results: Dict[str, AgentResult],
        synthesis_focus: str = "comprehensive"
    ) -> str:
        """
        Synthesize results from multiple agents into a coherent analysis

        Args:
            ticker: Stock ticker
            agent_results: Results from multiple agents
            synthesis_focus: "comprehensive", "investment", "risk", or "technical"

        Returns:
            Synthesized analysis text
        """
        # Prepare synthesis prompt
        synthesis_prompt = f"""Synthesize the following multi-agent financial analysis for {ticker}:

AGENT ANALYSIS RESULTS:
"""

        total_cost = 0.0
        total_confidence = 0.0
        agent_count = 0

        for agent_name, result in agent_results.items():
            if isinstance(result, AgentResult) and result.analysis:
                synthesis_prompt += f"\n{agent_name.upper()} ANALYSIS:\n{result.analysis}\n"
                total_cost += result.cost
                total_confidence += result.confidence
                agent_count += 1

        avg_confidence = total_confidence / agent_count if agent_count > 0 else 0

        synthesis_prompt += f"""
SYNTHESIS REQUIREMENTS:
- Focus: {synthesis_focus}
- Integrate insights from all {agent_count} agents
- Resolve any conflicting viewpoints
- Provide a coherent investment narrative
- Include specific actionable recommendations
- Highlight key risks and opportunities
- Average agent confidence: {avg_confidence:.2f}

Provide a comprehensive synthesis that combines all agent perspectives into a unified analysis."""

        # Get synthesis from LLM
        messages = [{"role": "user", "content": [{"text": synthesis_prompt}]}]

        system_message = """You are a senior portfolio manager synthesizing analysis from multiple financial experts.
        Your job is to integrate diverse perspectives into a coherent investment thesis. Resolve conflicts between
        different viewpoints and provide clear, actionable guidance."""

        response, cost_info = await self.bedrock_client.converse_async(
            messages=messages,
            system_message=system_message,
            max_tokens=3000,
            temperature=0.2
        )

        # Extract response text
        response_text = ""
        output_message = response.get('output', {}).get('message', {})
        content = output_message.get('content', [])

        for content_block in content:
            if 'text' in content_block:
                response_text += content_block['text']

        self.logger.logger.info(f"Synthesis completed for {ticker} - Total cost: ${total_cost + cost_info['total_cost']:.4f}")

        return response_text

    def get_agent_summary(self, agent_results: Dict[str, AgentResult]) -> Dict[str, Any]:
        """Get summary statistics from agent results"""
        if not agent_results:
            return {}

        total_cost = sum(result.cost for result in agent_results.values() if isinstance(result, AgentResult))
        avg_confidence = sum(result.confidence for result in agent_results.values() if isinstance(result, AgentResult)) / len(agent_results)
        total_execution_time = sum(result.execution_time_ms for result in agent_results.values() if isinstance(result, AgentResult))

        # Collect all recommendations
        all_recommendations = []
        for result in agent_results.values():
            if isinstance(result, AgentResult):
                all_recommendations.extend(result.recommendations)

        return {
            "agents_executed": len(agent_results),
            "total_cost": total_cost,
            "average_confidence": avg_confidence,
            "total_execution_time_ms": total_execution_time,
            "total_recommendations": len(all_recommendations),
            "agent_names": list(agent_results.keys())
        }
