"""
CrewAI Agents and Tasks for Chat Interface

This module defines specialized AI agents for different types of financial analysis
that can be orchestrated through the chat interface.
"""

from crewai import Agent, Task, Crew
from crewai.tools import BaseTool
from typing import Dict, List, Any, Optional
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta

try:
    from finmas.crews.bedrock_provider import get_crewai_bedrock_llm
except ImportError:
    # Fallback for when finmas modules are not available
    def get_crewai_bedrock_llm(model_id: str):
        from crewai import LLM
        return LLM(model=f"bedrock/{model_id}")


class StockDataTool(BaseTool):
    """Tool for fetching stock data."""
    
    name: str = "stock_data_fetcher"
    description: str = "Fetch stock price data and basic information"
    
    def _run(self, ticker: str, period: str = "6mo") -> str:
        """Fetch stock data for a given ticker."""
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period=period)
            info = stock.info
            
            if hist.empty:
                return f"No data found for ticker {ticker}"
            
            latest = hist.iloc[-1]
            prev = hist.iloc[-2] if len(hist) > 1 else latest
            
            change = latest['Close'] - prev['Close']
            change_pct = (change / prev['Close']) * 100
            
            result = f"""
Stock Data for {ticker}:
- Current Price: ${latest['Close']:.2f}
- Change: ${change:+.2f} ({change_pct:+.2f}%)
- Volume: {latest['Volume']:,}
- 52W High: ${hist['High'].max():.2f}
- 52W Low: ${hist['Low'].min():.2f}
- Market Cap: ${info.get('marketCap', 0):,}
- P/E Ratio: {info.get('trailingPE', 'N/A')}
- Company: {info.get('longName', ticker)}
- Sector: {info.get('sector', 'N/A')}
"""
            return result
            
        except Exception as e:
            return f"Error fetching data for {ticker}: {str(e)}"


class TechnicalAnalysisTool(BaseTool):
    """Tool for technical analysis."""
    
    name: str = "technical_analyzer"
    description: str = "Perform technical analysis on stock data"
    
    def _run(self, ticker: str, period: str = "6mo", indicators: List[str] = None) -> str:
        """Perform technical analysis."""
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period=period)
            
            if hist.empty:
                return f"No data found for ticker {ticker}"
            
            # Calculate basic indicators
            hist['SMA_20'] = hist['Close'].rolling(window=20).mean()
            hist['SMA_50'] = hist['Close'].rolling(window=50).mean()
            
            # RSI calculation
            delta = hist['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            hist['RSI'] = 100 - (100 / (1 + rs))
            
            latest = hist.iloc[-1]
            
            # Trend analysis
            if latest['Close'] > latest['SMA_20'] > latest['SMA_50']:
                trend = "Bullish"
            elif latest['Close'] < latest['SMA_20'] < latest['SMA_50']:
                trend = "Bearish"
            else:
                trend = "Neutral"
            
            # RSI interpretation
            if latest['RSI'] > 70:
                rsi_signal = "Overbought"
            elif latest['RSI'] < 30:
                rsi_signal = "Oversold"
            else:
                rsi_signal = "Neutral"
            
            result = f"""
Technical Analysis for {ticker}:
- Current Price: ${latest['Close']:.2f}
- 20-day SMA: ${latest['SMA_20']:.2f}
- 50-day SMA: ${latest['SMA_50']:.2f}
- RSI (14): {latest['RSI']:.2f}
- Trend: {trend}
- RSI Signal: {rsi_signal}
- Volume Trend: {'High' if latest['Volume'] > hist['Volume'].mean() else 'Normal'}
"""
            return result
            
        except Exception as e:
            return f"Error in technical analysis for {ticker}: {str(e)}"


class NewsAnalysisTool(BaseTool):
    """Tool for news sentiment analysis."""
    
    name: str = "news_analyzer"
    description: str = "Analyze news sentiment for a stock"
    
    def _run(self, ticker: str, days: int = 30) -> str:
        """Analyze news sentiment (simplified version)."""
        try:
            stock = yf.Ticker(ticker)
            news = stock.news
            
            if not news:
                return f"No recent news found for {ticker}"
            
            # Simple sentiment analysis based on title keywords
            positive_keywords = ['growth', 'profit', 'beat', 'strong', 'up', 'gain', 'success', 'positive']
            negative_keywords = ['loss', 'down', 'fall', 'weak', 'miss', 'decline', 'negative', 'concern']
            
            sentiment_scores = []
            recent_headlines = []
            
            for article in news[:10]:  # Analyze last 10 articles
                title = article.get('title', '').lower()
                recent_headlines.append(article.get('title', 'No title'))
                
                pos_count = sum(1 for word in positive_keywords if word in title)
                neg_count = sum(1 for word in negative_keywords if word in title)
                
                if pos_count > neg_count:
                    sentiment_scores.append(1)
                elif neg_count > pos_count:
                    sentiment_scores.append(-1)
                else:
                    sentiment_scores.append(0)
            
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
            
            if avg_sentiment > 0.2:
                overall_sentiment = "Positive"
            elif avg_sentiment < -0.2:
                overall_sentiment = "Negative"
            else:
                overall_sentiment = "Neutral"
            
            result = f"""
News Sentiment Analysis for {ticker}:
- Overall Sentiment: {overall_sentiment}
- Articles Analyzed: {len(sentiment_scores)}
- Sentiment Score: {avg_sentiment:.2f}

Recent Headlines:
"""
            for i, headline in enumerate(recent_headlines[:5], 1):
                result += f"{i}. {headline}\n"
            
            return result
            
        except Exception as e:
            return f"Error in news analysis for {ticker}: {str(e)}"


class ChatFinancialAgents:
    """Manager for financial analysis agents in chat interface."""
    
    def __init__(self, model_id: str = "us.anthropic.claude-3-5-sonnet-20241022-v2:0"):
        self.model_id = model_id
        self.llm = get_crewai_bedrock_llm(model_id=model_id)
        
        # Initialize tools
        self.stock_data_tool = StockDataTool()
        self.technical_tool = TechnicalAnalysisTool()
        self.news_tool = NewsAnalysisTool()
        
        # Create agents
        self.agents = self._create_agents()
    
    def _create_agents(self) -> Dict[str, Agent]:
        """Create specialized financial analysis agents."""
        
        # Technical Analyst Agent
        technical_analyst = Agent(
            role="Technical Analyst",
            goal="Analyze stock price movements, trends, and technical indicators to provide trading insights",
            backstory="""You are an expert technical analyst with years of experience in chart analysis, 
            pattern recognition, and technical indicators. You excel at identifying trends, support/resistance 
            levels, and providing actionable trading signals.""",
            tools=[self.stock_data_tool, self.technical_tool],
            llm=self.llm,
            verbose=True
        )
        
        # Fundamental Analyst Agent
        fundamental_analyst = Agent(
            role="Fundamental Analyst",
            goal="Evaluate company financials, business model, and intrinsic value for investment decisions",
            backstory="""You are a seasoned fundamental analyst who specializes in evaluating company 
            financials, business models, competitive positioning, and long-term value creation. You 
            provide thorough investment analysis based on financial health and growth prospects.""",
            tools=[self.stock_data_tool],
            llm=self.llm,
            verbose=True
        )
        
        # News & Sentiment Analyst Agent
        news_analyst = Agent(
            role="News & Sentiment Analyst",
            goal="Monitor news, market sentiment, and external factors affecting stock performance",
            backstory="""You are an expert in market sentiment analysis, news interpretation, and 
            understanding how external events impact stock prices. You track breaking news, earnings 
            reports, and market sentiment to provide timely insights.""",
            tools=[self.news_tool, self.stock_data_tool],
            llm=self.llm,
            verbose=True
        )
        
        # Investment Advisor Agent
        investment_advisor = Agent(
            role="Investment Advisor",
            goal="Synthesize all analysis to provide comprehensive investment recommendations",
            backstory="""You are a senior investment advisor who combines technical, fundamental, 
            and sentiment analysis to provide well-rounded investment recommendations. You consider 
            risk tolerance, investment horizon, and market conditions in your advice.""",
            tools=[self.stock_data_tool],
            llm=self.llm,
            verbose=True
        )
        
        return {
            "technical": technical_analyst,
            "fundamental": fundamental_analyst,
            "news": news_analyst,
            "advisor": investment_advisor
        }
    
    def create_analysis_crew(self, analysis_type: str, ticker: str, **kwargs) -> Crew:
        """Create a crew for specific analysis type."""
        
        if analysis_type == "technical":
            task = Task(
                description=f"Perform comprehensive technical analysis for {ticker}. "
                           f"Analyze price trends, technical indicators, support/resistance levels, "
                           f"and provide trading signals and recommendations.",
                agent=self.agents["technical"],
                expected_output="Detailed technical analysis report with specific trading recommendations"
            )
            return Crew(agents=[self.agents["technical"]], tasks=[task])
        
        elif analysis_type == "fundamental":
            task = Task(
                description=f"Conduct fundamental analysis for {ticker}. "
                           f"Evaluate financial health, business model, competitive position, "
                           f"and determine fair value and investment potential.",
                agent=self.agents["fundamental"],
                expected_output="Comprehensive fundamental analysis with valuation and investment recommendation"
            )
            return Crew(agents=[self.agents["fundamental"]], tasks=[task])
        
        elif analysis_type == "news":
            task = Task(
                description=f"Analyze news sentiment and market perception for {ticker}. "
                           f"Review recent news, earnings reports, and market sentiment "
                           f"to assess potential impact on stock price.",
                agent=self.agents["news"],
                expected_output="News sentiment analysis with potential market impact assessment"
            )
            return Crew(agents=[self.agents["news"]], tasks=[task])
        
        elif analysis_type == "combined":
            # Create tasks for each agent
            technical_task = Task(
                description=f"Perform technical analysis for {ticker}",
                agent=self.agents["technical"],
                expected_output="Technical analysis summary"
            )
            
            fundamental_task = Task(
                description=f"Perform fundamental analysis for {ticker}",
                agent=self.agents["fundamental"],
                expected_output="Fundamental analysis summary"
            )
            
            news_task = Task(
                description=f"Analyze news sentiment for {ticker}",
                agent=self.agents["news"],
                expected_output="News sentiment summary"
            )
            
            advisor_task = Task(
                description=f"Synthesize all analysis for {ticker} and provide final investment recommendation",
                agent=self.agents["advisor"],
                expected_output="Comprehensive investment recommendation",
                context=[technical_task, fundamental_task, news_task]
            )
            
            return Crew(
                agents=list(self.agents.values()),
                tasks=[technical_task, fundamental_task, news_task, advisor_task]
            )
        
        else:
            raise ValueError(f"Unknown analysis type: {analysis_type}")
    
    def run_analysis(self, analysis_type: str, ticker: str, **kwargs) -> str:
        """Run analysis using the appropriate crew."""
        try:
            crew = self.create_analysis_crew(analysis_type, ticker, **kwargs)
            result = crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Error running {analysis_type} analysis for {ticker}: {str(e)}"
