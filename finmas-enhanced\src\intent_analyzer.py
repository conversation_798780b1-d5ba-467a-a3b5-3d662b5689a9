"""
LLM-Driven Intent Analysis for Financial Queries

This module uses AWS Bedrock to analyze user queries and determine
intent without hardcoded patterns or regex matching.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .bedrock_client import BedrockClient
from utils.logging_utils import get_logger


@dataclass
class QueryIntent:
    """Represents the analyzed intent of a user query"""
    primary_intent: str
    secondary_intents: List[str]
    tickers: List[str]
    confidence: float
    analysis_types: List[str]
    timeframe: Optional[str] = None
    comparison_mode: bool = False
    specific_metrics: List[str] = None
    
    def __post_init__(self):
        if self.specific_metrics is None:
            self.specific_metrics = []


class IntentAnalyzer:
    """LLM-driven intent analysis for financial queries"""
    
    def __init__(self, bedrock_client: BedrockClient):
        self.bedrock_client = bedrock_client
        self.logger = get_logger()
        
        # System prompt for intent analysis
        self.intent_analysis_prompt = """You are an expert financial analysis intent classifier. Your job is to analyze user queries about stocks and financial markets to determine:

1. **Primary Intent**: The main goal of the user's query
2. **Secondary Intents**: Additional aspects they might be interested in
3. **Stock Tickers**: Any stock symbols mentioned or implied
4. **Analysis Types**: What kind of analysis they want
5. **Timeframe**: Investment or analysis timeframe
6. **Comparison Mode**: Whether they want to compare multiple stocks
7. **Specific Metrics**: Any particular financial metrics they're interested in

**Intent Categories:**
- analyze: General stock analysis
- technical: Technical analysis (charts, indicators, price action)
- fundamental: Fundamental analysis (financials, valuation, business)
- news: News analysis and sentiment
- recommendation: Investment recommendation or advice
- compare: Stock comparison
- portfolio: Portfolio-related queries
- market: General market analysis
- education: Learning about investing/analysis

**Analysis Types:**
- comprehensive: Full analysis across all dimensions
- technical: Technical indicators, charts, price trends
- fundamental: Financial statements, valuation, business analysis
- news: Recent news, sentiment, catalysts
- recommendation: Investment advice and ratings

**Timeframes:**
- short: 1-3 months
- medium: 3-12 months
- long: 1+ years

You must respond with a valid JSON object containing:
{
    "primary_intent": "string",
    "secondary_intents": ["string"],
    "tickers": ["string"],
    "confidence": 0.0-1.0,
    "analysis_types": ["string"],
    "timeframe": "string or null",
    "comparison_mode": boolean,
    "specific_metrics": ["string"]
}

Be very careful to extract actual stock ticker symbols. Common tickers include AAPL, MSFT, GOOGL, AMZN, TSLA, META, NVDA, etc. If a company name is mentioned, try to identify the correct ticker.

Examples:
- "Analyze Apple stock" → {"primary_intent": "analyze", "tickers": ["AAPL"], "analysis_types": ["comprehensive"]}
- "What's the technical outlook for Microsoft?" → {"primary_intent": "technical", "tickers": ["MSFT"], "analysis_types": ["technical"]}
- "Compare Tesla vs Ford" → {"primary_intent": "compare", "tickers": ["TSLA", "F"], "comparison_mode": true}
"""
    
    async def analyze_query(self, query: str) -> QueryIntent:
        """
        Analyze a user query to determine intent and extract information
        
        Args:
            query: The user's natural language query
            
        Returns:
            QueryIntent object with analyzed information
        """
        try:
            self.logger.logger.info(f"Analyzing query intent: {query[:100]}...")
            
            # Prepare messages for Bedrock
            messages = [
                {
                    "role": "user",
                    "content": [{"text": f"Analyze this financial query: '{query}'"}]
                }
            ]
            
            # Call Bedrock for intent analysis
            response, cost_info = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=self.intent_analysis_prompt,
                max_tokens=1000,
                temperature=0.1
            )
            
            # Extract response text
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Parse JSON response
            try:
                intent_data = json.loads(response_text.strip())
                
                # Validate and create QueryIntent
                query_intent = QueryIntent(
                    primary_intent=intent_data.get('primary_intent', 'analyze'),
                    secondary_intents=intent_data.get('secondary_intents', []),
                    tickers=intent_data.get('tickers', []),
                    confidence=float(intent_data.get('confidence', 0.8)),
                    analysis_types=intent_data.get('analysis_types', ['comprehensive']),
                    timeframe=intent_data.get('timeframe'),
                    comparison_mode=bool(intent_data.get('comparison_mode', False)),
                    specific_metrics=intent_data.get('specific_metrics', [])
                )
                
                self.logger.logger.info(
                    f"Intent analysis complete: {query_intent.primary_intent}, "
                    f"tickers: {query_intent.tickers}, confidence: {query_intent.confidence}"
                )
                
                return query_intent
                
            except json.JSONDecodeError as e:
                self.logger.logger.error(f"Failed to parse intent analysis JSON: {e}")
                self.logger.logger.error(f"Raw response: {response_text}")
                
                # Fallback to basic analysis
                return self._fallback_intent_analysis(query)
        
        except Exception as e:
            self.logger.logger.error(f"Intent analysis failed: {str(e)}")
            return self._fallback_intent_analysis(query)
    
    def _fallback_intent_analysis(self, query: str) -> QueryIntent:
        """Fallback intent analysis using simple heuristics"""
        query_lower = query.lower()
        
        # Basic intent detection
        if any(word in query_lower for word in ['compare', 'vs', 'versus', 'against']):
            primary_intent = 'compare'
            comparison_mode = True
        elif any(word in query_lower for word in ['technical', 'chart', 'price', 'trend']):
            primary_intent = 'technical'
            comparison_mode = False
        elif any(word in query_lower for word in ['fundamental', 'earnings', 'revenue', 'valuation']):
            primary_intent = 'fundamental'
            comparison_mode = False
        elif any(word in query_lower for word in ['news', 'sentiment', 'recent']):
            primary_intent = 'news'
            comparison_mode = False
        elif any(word in query_lower for word in ['recommend', 'should i', 'invest', 'buy', 'sell']):
            primary_intent = 'recommendation'
            comparison_mode = False
        else:
            primary_intent = 'analyze'
            comparison_mode = False
        
        # Basic ticker extraction (very simple fallback)
        import re
        potential_tickers = re.findall(r'\b[A-Z]{1,5}\b', query)
        
        # Filter to likely tickers
        common_tickers = [
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'TSLA', 'META', 'NVDA', 
            'NFLX', 'AMD', 'INTC', 'CRM', 'ORCL', 'ADBE', 'PYPL', 'UBER'
        ]
        
        tickers = [t for t in potential_tickers if t in common_tickers or len(t) <= 4]
        
        return QueryIntent(
            primary_intent=primary_intent,
            secondary_intents=[],
            tickers=tickers,
            confidence=0.6,  # Lower confidence for fallback
            analysis_types=['comprehensive'],
            timeframe=None,
            comparison_mode=comparison_mode,
            specific_metrics=[]
        )
    
    async def validate_tickers(self, tickers: List[str]) -> List[str]:
        """
        Validate ticker symbols using LLM
        
        Args:
            tickers: List of potential ticker symbols
            
        Returns:
            List of validated ticker symbols
        """
        if not tickers:
            return []
        
        try:
            validation_prompt = """You are a financial data expert. Validate these potential stock ticker symbols and return only the valid ones.

Rules:
1. Return only real, actively traded stock ticker symbols
2. Convert company names to their correct ticker symbols
3. Remove any invalid or non-existent tickers
4. Return as a JSON array of strings

Examples:
- "Apple" → ["AAPL"]
- "Microsoft" → ["MSFT"] 
- "Google" → ["GOOGL"]
- "Amazon" → ["AMZN"]
- "Tesla" → ["TSLA"]

Respond with only a JSON array, no other text."""
            
            messages = [
                {
                    "role": "user", 
                    "content": [{"text": f"Validate these tickers: {tickers}"}]
                }
            ]
            
            response, _ = await self.bedrock_client.converse_async(
                messages=messages,
                system_message=validation_prompt,
                max_tokens=500,
                temperature=0.1
            )
            
            # Extract response
            response_text = ""
            output_message = response.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    response_text += content_block['text']
            
            # Parse JSON response
            validated_tickers = json.loads(response_text.strip())
            
            if isinstance(validated_tickers, list):
                self.logger.logger.info(f"Validated tickers: {tickers} → {validated_tickers}")
                return validated_tickers
            else:
                return tickers  # Fallback to original
                
        except Exception as e:
            self.logger.logger.error(f"Ticker validation failed: {str(e)}")
            return tickers  # Fallback to original tickers
