# FinMAS Enhanced - Financial Multi-Agent System

An enhanced version of the FinMAS financial analysis system with improved architecture, comprehensive logging, cost tracking, and complete agent execution.

## 🚀 Features

### Core Capabilities
- **Complete Agent Execution**: All CrewAI agents run to completion with full, untruncated output
- **Comprehensive Analysis**: Technical, Fundamental, News, and Investment Advisory analysis
- **AWS Bedrock Integration**: Enhanced Bedrock Converse API with function calling
- **Real-time Cost Tracking**: Monitor AWS API costs and token usage in real-time
- **Advanced Logging**: Comprehensive logging of all function calls and performance metrics

### Enhanced Architecture
- **Modular Design**: Clean separation between utilities, agents, and UI components
- **Cost Analytics**: Detailed cost breakdowns, trends, and optimization recommendations
- **Performance Monitoring**: Track execution times, success rates, and efficiency metrics
- **Streamlit UI**: Focused interface for presentation and user interaction

## 📁 Directory Structure

```
finmas-enhanced/
├── utils/                      # Core utilities
│   ├── __init__.py
│   ├── logging_utils.py        # Comprehensive logging system
│   ├── bedrock_utils.py        # Enhanced Bedrock client with cost tracking
│   ├── crewai_utils.py         # CrewAI orchestration utilities
│   └── analytics.py            # Advanced analytics and reporting
├── agents/                     # Financial analysis agents
│   ├── __init__.py
│   └── financial_agents.py     # Specialized agent definitions
├── ui/                         # User interface components
│   ├── __init__.py
│   └── streamlit_ui.py         # Streamlit application
├── logs/                       # Log files and session data
├── app.py                      # Main application entry point
├── main.py                     # Application orchestrator
├── config.py                   # Configuration settings
├── requirements.txt            # Dependencies
└── README.md                   # This file
```

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- AWS Account with Bedrock access
- AWS credentials configured

### Setup

1. **Clone or create the directory**:
   ```bash
   cd finmas-enhanced
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure AWS credentials**:
   ```bash
   # Option 1: AWS CLI
   aws configure
   
   # Option 2: Environment variables
   export AWS_ACCESS_KEY_ID=your_access_key
   export AWS_SECRET_ACCESS_KEY=your_secret_key
   export AWS_DEFAULT_REGION=us-east-1
   ```

4. **Run the application**:
   ```bash
   streamlit run app.py
   ```

## 🎯 Usage

### Basic Analysis

1. **Start the application**:
   ```bash
   streamlit run app.py
   ```

2. **Enter a stock ticker** (e.g., AAPL, MSFT, GOOGL)

3. **Configure analysis parameters**:
   - Select Bedrock model
   - Adjust temperature and max tokens
   - Choose analysis components

4. **Run complete analysis** and view results

### Advanced Features

#### Cost Tracking
- Real-time cost monitoring in the header
- Detailed cost analytics dashboard
- Budget alerts and optimization recommendations

#### Performance Analytics
- Agent execution metrics
- Performance trends and optimization insights
- Detailed execution reports

#### Export and Reporting
- Download comprehensive analysis reports
- Export session data as JSON
- Generate cost and performance summaries

## 🔧 Configuration

### Model Selection
Choose from available AWS Bedrock models:
- **Claude 3.5 Sonnet**: Most capable, premium cost
- **Claude 3 Sonnet**: Balanced performance and cost
- **Claude 3 Haiku**: Fastest and most economical

### Cost Management
- Set budget limits and alerts
- Monitor token usage and costs
- Get optimization recommendations

### Analysis Components
Enable/disable specific analysis types:
- Technical Analysis
- Fundamental Analysis
- News Analysis
- Investment Recommendation

## 📊 Analysis Output

### Technical Analysis
- Price trend analysis with specific levels
- Technical indicator values and signals
- Support/resistance identification
- Trading recommendations with entry/exit points

### Fundamental Analysis
- Financial health assessment with metrics
- Revenue and earnings growth analysis
- Valuation analysis with peer comparisons
- Investment recommendation with price targets

### News Analysis
- Recent news impact assessment
- Market sentiment analysis
- Catalyst identification
- Risk factor evaluation

### Investment Recommendation
- Synthesized investment rating
- Specific price targets and timelines
- Risk assessment and mitigation
- Portfolio allocation recommendations

## 💰 Cost Tracking Features

### Real-time Monitoring
- Session cost tracking
- Token usage monitoring
- API call statistics
- Model-specific cost breakdown

### Analytics Dashboard
- Cost trends over time
- Token usage patterns
- Performance vs cost analysis
- Optimization recommendations

### Budget Management
- Set session budget limits
- Receive cost alerts
- Predict remaining session costs
- Export cost reports

## 🔍 Logging and Monitoring

### Comprehensive Logging
- Function call tracking with timestamps
- AWS Bedrock API call logging
- Agent execution monitoring
- Performance metrics collection

### Session Data
- Export complete session data
- Generate execution summaries
- Track cost and performance trends
- Analyze optimization opportunities

## 🚨 Error Handling

### Robust Error Management
- Retry logic for API calls
- Graceful degradation for missing data
- Comprehensive error logging
- User-friendly error messages

### Monitoring and Alerts
- Real-time cost alerts
- Performance degradation warnings
- Budget limit notifications
- Optimization recommendations

## 🔒 Security

### AWS Integration
- Secure credential management
- IAM role-based access
- Encrypted API communications
- Session data protection

### Data Privacy
- No persistent storage of sensitive data
- Local session management
- Configurable data retention
- Secure logging practices

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Install development dependencies
4. Run tests and validation
5. Submit a pull request

### Code Standards
- Follow PEP 8 style guidelines
- Include comprehensive docstrings
- Add unit tests for new features
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Common Issues
- **Import Errors**: Ensure all dependencies are installed
- **AWS Credentials**: Verify AWS configuration and permissions
- **Cost Alerts**: Check budget settings and usage patterns

### Getting Help
- Check the logs directory for detailed error information
- Review the configuration settings
- Ensure AWS Bedrock access is properly configured

## 🔄 Updates and Roadmap

### Recent Enhancements
- ✅ Comprehensive cost tracking
- ✅ Enhanced logging system
- ✅ Modular architecture
- ✅ Real-time analytics

### Planned Features
- 🔄 Advanced portfolio analysis
- 🔄 Multi-timeframe analysis
- 🔄 Custom agent configurations
- 🔄 API endpoint for programmatic access

---

**FinMAS Enhanced** - Empowering financial analysis with AI agents and comprehensive monitoring.
