2025-06-21 12:47:18,456 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_124718
2025-06-21 12:47:18,456 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_124718
2025-06-21 12:47:18,456 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_124718
2025-06-21 12:47:18,456 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 12:47:18,456 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 12:47:18,608 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 12:47:18,608 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 12:47:20,446 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 12:47:20,446 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 12:47:20,446 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 12:47:20,446 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 12:47:20,446 - finmas_enhanced - INFO - Agent Coordinator initialized with 6 specialized agents
2025-06-21 12:47:20,446 - finmas_enhanced - INFO - Agent Coordinator initialized with 6 specialized agents
2025-06-21 12:47:20,447 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 12:47:20,447 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 12:47:55,714 - finmas_enhanced - INFO - Analyzing query: how did aws performed vs microsoft and meta last year...
2025-06-21 12:47:55,714 - finmas_enhanced - INFO - Analyzing query: how did aws performed vs microsoft and meta last year...
2025-06-21 12:47:55,715 - finmas_enhanced - INFO - Analyzing query intent: how did aws performed vs microsoft and meta last year...
2025-06-21 12:47:55,715 - finmas_enhanced - INFO - Analyzing query intent: how did aws performed vs microsoft and meta last year...
2025-06-21 12:47:59,485 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 603+167=770, Cost: $0.0043
2025-06-21 12:47:59,485 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 603+167=770, Cost: $0.0043
2025-06-21 12:47:59,485 - finmas_enhanced - ERROR - Failed to parse intent analysis JSON: Extra data: line 12 column 1 (char 321)
2025-06-21 12:47:59,485 - finmas_enhanced - ERROR - Failed to parse intent analysis JSON: Extra data: line 12 column 1 (char 321)
2025-06-21 12:47:59,485 - finmas_enhanced - ERROR - Raw response: {
    "primary_intent": "compare",
    "secondary_intents": ["analyze"],
    "tickers": ["AMZN", "MSFT", "META"],
    "confidence": 0.95,
    "analysis_types": ["fundamental", "comprehensive"],
    "timeframe": "medium",
    "comparison_mode": true,
    "specific_metrics": ["revenue", "growth", "market_performance"]
}

Note: While AWS is mentioned, it's a division of Amazon (AMZN), so I included AMZN as the relevant ticker. The query is clearly asking for a comparative analysis of these tech companies' performance over the past year, focusing on their business performance and market results.
2025-06-21 12:47:59,485 - finmas_enhanced - ERROR - Raw response: {
    "primary_intent": "compare",
    "secondary_intents": ["analyze"],
    "tickers": ["AMZN", "MSFT", "META"],
    "confidence": 0.95,
    "analysis_types": ["fundamental", "comprehensive"],
    "timeframe": "medium",
    "comparison_mode": true,
    "specific_metrics": ["revenue", "growth", "market_performance"]
}

Note: While AWS is mentioned, it's a division of Amazon (AMZN), so I included AMZN as the relevant ticker. The query is clearly asking for a comparative analysis of these tech companies' performance over the past year, focusing on their business performance and market results.
2025-06-21 12:47:59,485 - finmas_enhanced - INFO - Intent analysis: compare, tickers: [], confidence: 0.6
2025-06-21 12:47:59,485 - finmas_enhanced - INFO - Intent analysis: compare, tickers: [], confidence: 0.6
